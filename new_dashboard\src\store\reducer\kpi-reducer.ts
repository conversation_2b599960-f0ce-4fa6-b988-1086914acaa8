import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
   KPIMeta,
   KPIRange,
   KPIStoreRange,
   Connector,
   KpiData,
   ConnectorKpiData,
   alertsAndOpportunities,
} from '../../pages/dashboard/utils/interface';
import { defineds } from '../../pages/dashboard/utils/default-ranges';
import { getPrevPeriod } from '../../pages/dashboard/utils/helpers';
import { format, startOfDay, endOfDay } from 'date-fns';

interface KPIState {
   dateRange: KPIStoreRange;
   prevRange: KPIStoreRange;
   groupBy: string;
   updatedPins: string[];
   kpiMeta: KPIMeta[];
   pinnedKpi: Connector;
   connectorData: Connector;
   alertsAndOpportunities: Record<string, alertsAndOpportunities>;
   currency: { meta: string; google: string };
}

const defaultStart = startOfDay(defineds.startOfLast7thDay);
const defaultEnd = endOfDay(defineds.endOfYesterday);

const defaultPrevRange: KPIRange = getPrevPeriod({
   start: defaultStart,
   end: defaultEnd,
});

export const initialKPIState: KPIState = {
   dateRange: {
      start: format(defaultStart, "yyyy-MM-dd'T'HH:mm:ss"),
      end: format(defaultEnd, "yyyy-MM-dd'T'HH:mm:ss"),
   },
   prevRange: {
      start: format(
         startOfDay(defaultPrevRange.start),
         "yyyy-MM-dd'T'HH:mm:ss",
      ),
      end: format(endOfDay(defaultPrevRange.end), "yyyy-MM-dd'T'HH:mm:ss"),
   },
   groupBy: 'day',
   updatedPins: [],
   kpiMeta: [],
   pinnedKpi: {
      pinned: {},
   },
   connectorData: {},
   alertsAndOpportunities: {},
   currency: {
      meta: '',
      google: '',
   },
};

const kpiSlice = createSlice({
   name: 'kpi',
   initialState: initialKPIState,
   reducers: {
      setdateRange: (state: KPIState, action: PayloadAction<KPIStoreRange>) => {
         state.dateRange = action.payload;
      },
      resetDateRange: (state: KPIState) => {
         state.dateRange = {
            start: format(
               startOfDay(defineds.startOfLast7thDay),
               "yyyy-MM-dd'T'HH:mm:ss",
            ),
            end: format(
               endOfDay(defineds.endOfYesterday),
               "yyyy-MM-dd'T'HH:mm:ss",
            ),
         };
         state.prevRange = {
            start: format(
               startOfDay(defaultPrevRange.start),
               "yyyy-MM-dd'T'HH:mm:ss",
            ),
            end: format(
               endOfDay(defaultPrevRange.end),
               "yyyy-MM-dd'T'HH:mm:ss",
            ),
         };
      },
      setprevRange: (state: KPIState, action: PayloadAction<KPIStoreRange>) => {
         state.prevRange = action.payload;
      },
      setGroupBy: (state: KPIState, action: PayloadAction<string>) => {
         state.groupBy = action.payload;
      },
      setUpdatedPins: (state: KPIState, action: PayloadAction<string>) => {
         state.updatedPins.push(action.payload);
      },
      setKpiMeta: (state: KPIState, action: PayloadAction<KPIMeta[]>) => {
         state.kpiMeta = action.payload;
      },

      // 🔹 toggle pinned flag directly inside metaData
      togglePinned: (state, action: PayloadAction<string>) => {
         state.kpiMeta = state.kpiMeta.map((kpi) =>
            kpi.kpi === action.payload ? { ...kpi, pinned: !kpi.pinned } : kpi,
         );
      },

      // 🔹 toggle visible flag directly inside metaData
      toggleVisible: (state, action: PayloadAction<string>) => {
         state.kpiMeta = state.kpiMeta.map((kpi) =>
            kpi.kpi === action.payload
               ? { ...kpi, visible: !kpi.visible }
               : kpi,
         );
      },

      setPinned: (state, action: PayloadAction<Connector>) => {
         state.pinnedKpi = action.payload;
      },
      togglePinnedKPIs: (state, action: PayloadAction<KpiData>) => {
         const key = action.payload.kpi_names;
         const newPinned = { ...state.pinnedKpi.pinned };

         if (newPinned[key]) {
            // unpin
            delete newPinned[key];
         } else {
            // pin: override pinned to true
            newPinned[key] = action.payload;
         }

         state.pinnedKpi.pinned = newPinned;
      },
      setConnectorData: (
         state,
         action: PayloadAction<{ key: string; data: ConnectorKpiData }>,
      ) => {
         state.connectorData[action.payload.key] = action.payload.data;
      },

      togglePinnedVisible: (
         state,
         action: PayloadAction<{ category: string; kpi: string; add: boolean }>,
      ) => {
         const { category, kpi, add } = action.payload;

         // 1️⃣ Update kpiMeta: set visible and pinned based on add/remove
         state.kpiMeta = state.kpiMeta.map((item) =>
            item.kpi === kpi && item.category === category
               ? { ...item, pinned: add }
               : item,
         );

         // 2️⃣ Get connector data for this KPI if adding
         if (add) {
            const connectorCategoryData = state.connectorData[category];
            if (!connectorCategoryData) return;

            const kpiData = connectorCategoryData[kpi];
            if (!kpiData) return;

            // 3️⃣ Add to pinnedKpi
            state.pinnedKpi.pinned = {
               ...state.pinnedKpi.pinned,
               [kpi]: kpiData,
            };
         } else {
            // 🔹 Remove from pinnedKpi if removing
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { [kpi]: _, ...rest } = state.pinnedKpi.pinned;
            state.pinnedKpi.pinned = rest;
         }
      },
      setAlertsAndOpportunities: (
         state,
         action: PayloadAction<{
            kpiName: string;
            direction: 'up' | 'down';
            isPositive: boolean;
            percentage: string;
            message: string;
            category: string;
            remove: boolean;
         }>,
      ) => {
         state.alertsAndOpportunities[action.payload.kpiName] = action.payload;
      },
      removeAlertOrOpportunity: (state, action: PayloadAction<string>) => {
         delete state.alertsAndOpportunities[action.payload];
      },

      setCurrency: (
         state,
         action: PayloadAction<{ meta: string; google: string }>,
      ) => {
         state.currency = action.payload;
      },
   },
});

export const {
   setdateRange,
   resetDateRange,
   setprevRange,
   setGroupBy,
   setUpdatedPins,
   setKpiMeta,
   togglePinned,
   toggleVisible,
   setPinned,
   togglePinnedKPIs,
   setConnectorData,
   togglePinnedVisible,
   setAlertsAndOpportunities,
   removeAlertOrOpportunity,
   setCurrency,
} = kpiSlice.actions;

export default kpiSlice.reducer;
