import { WrapI<PERSON>, VStack, Flex, Text, useColorMode } from '@chakra-ui/react';
import { useState } from 'react';
import { GrCircleQuestion } from 'react-icons/gr';
import TooltipContent from '@/pages/dashboard/components/tooltip-content';
import { GoAlert } from 'react-icons/go';
import ViewAllAlertsOpp from './ViewAllAlertsOpp';
import { IoIosTrendingUp } from 'react-icons/io';
import { Tooltip } from '@chakra-ui/react';
import './line-chart.scss';
import { useNavigate } from 'react-router-dom';
import {
   setCurrentSessionID,
   setCurrentMode,
   setKpiPrompts,
} from '../../../store/reducer/analytics-agent-reducer';
import { format } from 'date-fns';
import { useAppSelector } from '@/store/store';
import { useAppDispatch } from '../../../store/store';
import { alertsAndOpportunities } from '@/pages/dashboard/utils/interface';
import AlertCategoryImages from '@/pages/dashboard/utils/alert-images';
import { HiSparkles } from 'react-icons/hi2';

const AlertsOpportunitiesCard = () => {
   const { colorMode } = useColorMode();
   const [showRightIcons, setShowRightIcons] = useState(false);

   const navigate = useNavigate();
   const dispatch = useAppDispatch();
   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);

   const allAlertsAndOpportunities = useAppSelector(
      (state) => state.kpi.alertsAndOpportunities,
   );
   /*  if(Object.keys(allAlertsAndOpportunities).length === 0 )
    return null;*/
   const handleNavigate = (alert: alertsAndOpportunities) => {
      const { kpiName, direction, percentage } = alert;
      const currentPeriod = `${format(new Date(dateRange.start), 'MMM d')} - ${format(new Date(dateRange.end), 'MMM d')}`;
      const previousPeriod = `${format(new Date(prevRange.start), 'MMM d')} - ${format(new Date(prevRange.end), 'MMM d')}`;
      const buildAgentPrompt = () => {
         return `
Find the root cause and analyze the key factors that led to this KPI change.

The KPI **${kpiName}** has ${direction === 'up' ? 'increased' : 'decreased'} by **${percentage}%** when comparing the period **${currentPeriod}** vs **${previousPeriod}**.
  `;
      };
      dispatch(setCurrentSessionID(''));
      dispatch(setCurrentMode('data-analyst'));
      dispatch(
         setKpiPrompts({
            displayPrompt: `Analyze why ${alert.message} when comparing the period ${currentPeriod} vs ${previousPeriod}`,
            aiPrompt: buildAgentPrompt(),
         }),
      );

      navigate('/marco/analytics-agent');
   };

   return (
      <WrapItem
         minWidth={100}
         flex='1'
         background={colorMode === 'dark' ? 'gray.800' : 'white'}
         boxShadow={
            colorMode === 'dark'
               ? '1px 1px 10px 1px #00000033'
               : '1px 1px 10px 1px #cccccc33'
         }
         padding={5}
         borderRadius={5}
         position={'relative'}
         onMouseOver={() => setShowRightIcons(true)}
         onMouseLeave={() => setShowRightIcons(false)}
         className='kpi-item'
      >
         <VStack width='100%' alignItems={'flex-start'}>
            {showRightIcons && (
               <div className='right-items'>
                  <TooltipContent
                     placement='top'
                     hasArrow
                     label='Alerts & Opportunities info'
                  >
                     <GrCircleQuestion />
                  </TooltipContent>
               </div>
            )}

            <Flex width='100%' direction='column' gap={2}>
               <Text fontSize={14} fontWeight={600}>
                  Alerts & Opportunities
               </Text>

               {Object.keys(allAlertsAndOpportunities).length === 0 ? (
                  <Flex
                     flex='1'
                     width='100%'
                     align='center'
                     justify='center'
                     direction='column'
                     textAlign='center'
                     minHeight='120px'
                  >
                     <Text
                        fontSize={13}
                        fontWeight={500}
                        color={colorMode === 'dark' ? 'gray.400' : 'gray.500'}
                     >
                        No Alerts insights found for the selected date range
                     </Text>
                  </Flex>
               ) : (
                  Object.values(allAlertsAndOpportunities)
                     .slice(0, 4)
                     .map((alert, idx) => (
                        <Flex key={idx} align='center' gap={2}>
                           {alert.isPositive === false ? (
                              <Flex
                                 align='center'
                                 justify='center'
                                 bg='#ffeaea'
                                 color='#e03131'
                                 borderRadius='full'
                                 boxSize='40px'
                                 pb='1px'
                              >
                                 <GoAlert size={20} />
                              </Flex>
                           ) : (
                              <Flex
                                 align='center'
                                 justify='center'
                                 bg='#e8f8ec'
                                 color='#2b8a3e'
                                 borderRadius='full'
                                 boxSize='40px'
                              >
                                 <IoIosTrendingUp size={20} />
                              </Flex>
                           )}

                           <Flex
                              align='center'
                              gap={2}
                              w='100%'
                              justify='space-between'
                           >
                              <Flex align='center' gap={2} minW={0}>
                                 <AlertCategoryImages
                                    category={alert.category}
                                 />
                                 <Text fontSize={12} noOfLines={1} minW={0}>
                                    <Text as='span'>{alert.kpiName}</Text> is{' '}
                                    <Text as='span' fontWeight='600'>
                                       {alert.direction === 'up'
                                          ? 'up'
                                          : 'down'}
                                    </Text>{' '}
                                    by{' '}
                                    <Text
                                       as='span'
                                       color={
                                          alert.isPositive
                                             ? 'green.500'
                                             : 'red.500'
                                       }
                                       fontWeight='bold'
                                    >
                                       {alert.percentage}%
                                    </Text>
                                 </Text>
                              </Flex>

                              <Tooltip label='Analyze with AI CMO' hasArrow>
                                 <Flex
                                    align='center'
                                    gap={1}
                                    px={2}
                                    py={1}
                                    bg='#E9E1FF'
                                    borderRadius='md'
                                    cursor='pointer'
                                    _hover={{ bg: 'purple.100' }}
                                    onClick={() => handleNavigate(alert)}
                                    ml='auto'
                                    minW='fit-content'
                                 >
                                    <HiSparkles color='#7F56D9' size={13} />
                                    <Text fontSize='11px'>Know Why ?</Text>
                                 </Flex>
                              </Tooltip>
                           </Flex>
                        </Flex>
                     ))
               )}
            </Flex>
            {Object.keys(allAlertsAndOpportunities).length > 4 && (
               <ViewAllAlertsOpp
                  allAlertsAndOpportunities={allAlertsAndOpportunities}
               />
            )}
         </VStack>
      </WrapItem>
   );
};

export default AlertsOpportunitiesCard;
