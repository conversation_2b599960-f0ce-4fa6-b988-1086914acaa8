import { modalTypes } from '@/components/modals/modal-types';
import { openModal } from '@/store/reducer/modal-reducer';
import { Text, /*useColorModeValue, */ Flex, Button } from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { ChartProp } from '../utils/interface';
import { HiSparkles } from 'react-icons/hi2';
import { handleRootCauseAction } from '../utils/HandleAnamolyClick';
import { useAppSelector } from '@/store/store';
import { useNavigate } from 'react-router-dom';
function ViewDetails(props: ChartProp) {
   const { kpiDetails, anomaly } = props;
   const dispatch = useDispatch();
   const navigate = useNavigate();

   /*const { generalSettings } = useAppSelector((state) => state.settings);*/
   const { prevRange, currency: kpiCurrency } = useAppSelector(
      (state) => state.kpi,
   );
   //const linkColor = useColorModeValue('#337CDF', '#63B3ED');
   if (!kpiDetails?.current_allData?.length) return null;

   const handleViewOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.KPI_VIEW_DETAILS,
            modalProps: { kpiDetails, anomaly },
         }),
      );
   };

   const handleDeepDive = async () => {
      await handleRootCauseAction({
         kpiDetails,
         prevRange,
         kpiCurrency,
         dispatch,
         navigate,
      });
   };

   const isAnomalyDefined = anomaly !== undefined && anomaly !== null;
   const isSupportedCategory =
      kpiDetails.category === 'facebookads' ||
      kpiDetails.category === 'googleads';
   const showRootCause = isAnomalyDefined && isSupportedCategory;

   const buttonColors =
      anomaly === true
         ? {
              bg: '#E6FFFA',
              borderColor: '#38A169',
              color: '#38A169',
              hoverBg: '#38A169',
              hoverColor: 'white',
              hoverBorderColor: '#38A169',
           }
         : {
              bg: '#FED7D7',
              borderColor: '#FF5630',
              color: '#FF5630',
              hoverBg: '#FF5630',
              hoverColor: 'white',
              hoverBorderColor: '#FF5630',
           };

   return (
      <Flex justify='space-between' align='center' width='98%'>
         <Text
            cursor={'pointer'}
            onClick={handleViewOpen}
            fontSize={14}
            textDecoration={'underline'}
            textAlign={'left'}
            color='#4A4A4F'
         >
            More Details
         </Text>

         {showRootCause && (
            <Button
               className='deep-dive-btn'
               size='sm'
               variant='outline'
               borderWidth={2}
               onClick={() => {
                  void handleDeepDive();
               }}
               bg={buttonColors.bg}
               borderColor={buttonColors.borderColor}
               color={buttonColors.color}
               _hover={{
                  bg: buttonColors.hoverBg,
                  borderColor: buttonColors.hoverBorderColor,
                  color: buttonColors.hoverColor,
                  transform: 'translateY(-1px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  transition: 'all 0.2s ease-in-out',
               }}
               _active={{
                  transform: 'translateY(0px)',
                  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
               }}
               leftIcon={<HiSparkles size={14} />}
               fontSize={12}
               fontWeight={650}
               height='28px'
               borderRadius='6px'
               transition='all 0.2s ease-in-out'
            >
               Deep Dive
            </Button>
         )}
      </Flex>
   );
}

export default ViewDetails;
