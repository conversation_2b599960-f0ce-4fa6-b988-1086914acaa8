import { useNavigate } from 'react-router-dom';
import { AgentCardProps } from './cards/agent-card';
import Agent<PERSON><PERSON> from './cards/agent-card';
import AlertingAgent from '../../../assets/icons/alerting-agent-icon.webp';
import MetaAdsAgentImage from '../../../assets/icons/meta-ads-manager-icon.webp';
import AnalyticsAgentImage from '../../../assets/icons/analytics-agent.webp';
import DiagnosticAgentImage from '../../../assets/icons/diagnostic-agent.webp';
import { useAppDispatch } from '@/store/store';
import { setCurrentAgent } from '@/store/reducer/marco-reducer';
import ChooseAlertType from './dialogs/choose-alert-type';
import ChooseViewAlerts from './dialogs/choose-view-alerts';

const MarcoAgents = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   const AGENTS: AgentCardProps[] = [
      {
         image: AlertingAgent,
         heading: 'Alerting Agent',
         description:
            'The Alerting Agent monitors KPIs, detects anomalies, and ensures accuracy. With the Diagnostic Agent, it identifies root causes and suggests fixes.',
         primaryButtonText: '',
         renderPrimaryButton: () => <ChooseAlertType />,
         secondaryButtonText: '',
         renderSecondaryButton: () => <ChooseViewAlerts />,
      },
      {
         image: MetaAdsAgentImage,
         heading: 'Meta Ads Manager',
         description:
            'The Meta Ads Manager automates campaign creation, audience targeting, and bid optimization to maximize ad performance. It continuously analyzes data to refine strategies and improve ROI.',
         primaryButtonText: 'Create Campaign',
         primaryButtonDisabled: true,
         primaryButtonAction: () => navigate('/marco/meta-ads-manager'),
         secondaryButtonText: 'Manage Campaign',
         secondaryButtonDisabled: true,
      },
      {
         image: AnalyticsAgentImage,
         heading: 'Analytics Agent',
         description:
            'The Analytics Agent connects user queries to data insights, retrieving key ad metrics and providing real-time, actionable results.',
         primaryButtonText: 'Start Analysis',
         primaryButtonDisabled: false,
         primaryButtonAction: () => {
            dispatch(setCurrentAgent('analytics-agent'));
            navigate('/marco/analytics-agent');
         },
      },
      {
         image: DiagnosticAgentImage,
         heading: 'Diagnostic Agent',
         description:
            'The Diagnostic Agent analyzes KPIs to detect performance drops, examining campaign metrics, engagement, conversions, and ad spend efficiency.',
         primaryButtonText: 'Start Diagnosis',
         primaryButtonDisabled: true,
      },
   ];

   return (
      <div className='w-full h-full px-10 py-8'>
         <div className='w-full grid gap-8 [grid-template-columns:repeat(auto-fill,minmax(600px,1fr))]'>
            {AGENTS.map((agent) => (
               <AgentCard key={agent.heading} {...agent} />
            ))}
         </div>
      </div>
   );
};

export default MarcoAgents;
