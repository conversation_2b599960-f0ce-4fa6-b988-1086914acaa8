import { AxiosResponse } from 'axios';
import dashboardApiAgent from '../../agent';
import {
   AnomalyCause,
   DashboardCurrency,
   DataDashboardResponse,
   KPIAnomalyCausePayload,
   KPIMeta,
   KPISummaryPayload,
   PayloadInterface,
   PinPayload,
   PinVisibleOrderPayload,
   VisblePayload,
} from '@/pages/dashboard/utils/interface';

export interface EndPoints {
   // getKpiData: (payload: KPIPayload) => Promise<AxiosResponse<FinalAgg>>;
   fetchDashboardData: (
      payload: PayloadInterface,
   ) => Promise<AxiosResponse<DataDashboardResponse>>;
   fetchPinnedData: (
      payload: unknown,
   ) => Promise<AxiosResponse<DataDashboardResponse>>;
   getKpiSummary: (
      payload: KPISummaryPayload,
   ) => Promise<AxiosResponse<string>>;
   getKpiMeta: (clientId: string) => Promise<AxiosResponse<KPIMeta[]>>;
   getCurrency: (clientId: string) => Promise<AxiosResponse<DashboardCurrency>>;
   updatePinned: (payload: PinPayload) => Promise<AxiosResponse<string>>;
   updateVisible: (payload: VisblePayload) => Promise<AxiosResponse<string>>;
   updatePinVisibleOrder: (
      payload: PinVisibleOrderPayload,
   ) => Promise<AxiosResponse<string>>;
   getAnomalyCause: (
      payload: KPIAnomalyCausePayload,
   ) => Promise<AxiosResponse<AnomalyCause>>;
}

const endPoints: EndPoints = {
   // getKpiData: async (payload) => {
   //    const data = await dashboardApiAgent.post('/kpi/data', payload);
   //    return data;
   // },

   fetchDashboardData: (payload) =>
      dashboardApiAgent.get('/dash/data', {
         params: payload,
      }),
   fetchPinnedData: (payload) =>
      dashboardApiAgent.post('/dash/pinned-data', payload),
   getKpiSummary: async (payload) => {
      const summary = await dashboardApiAgent.post('/kpi/summary', payload);
      return summary;
   },
   getKpiMeta: async (clientId) => {
      const metaData = await dashboardApiAgent.get(`/kpi/meta/${clientId}`);
      return metaData;
   },
   getCurrency: async (clientId) => {
      const currencyData = await dashboardApiAgent.get(
         `/dash/currency/${clientId}`,
      );
      return currencyData;
   },
   updatePinned: async (payload) => {
      const res = await dashboardApiAgent.post('/kpi/pinned', payload);
      return res;
   },
   updateVisible: async (payload) => {
      const res = await dashboardApiAgent.post('/kpi/visible', payload);
      return res;
   },
   updatePinVisibleOrder: async (payload) => {
      const res = await dashboardApiAgent.post('/kpi/order', payload);
      return res;
   },
   getAnomalyCause: async (payload) => {
      const res = await dashboardApiAgent.post(
         '/kpi/anomaly-root-cause',
         payload,
      );
      return res;
   },
};

export default endPoints;
