import { useEffect, useState, useRef } from 'react';

interface TypewriterOptions {
   prompts: string[];
   prefix?: string; // always static beginning
   typingSpeed?: number;
   deletingSpeed?: number;
   pauseDuration?: number;
   loop?: boolean;
   enabled?: boolean;
   maxWords?: number;
}

export const useTypewriterPlaceholder = ({
   prompts,
   prefix = 'Ask AI CMO to know ',
   typingSpeed = 150,
   deletingSpeed = 200,
   pauseDuration = 1500,
   //loop = true,
   enabled = true,
   maxWords = 8,
}: TypewriterOptions) => {
   const [text, setText] = useState(prefix);
   const [index, setIndex] = useState(0);
   const [isDeleting, setIsDeleting] = useState(false);
   const timeoutRef = useRef<NodeJS.Timeout | null>(null);

   useEffect(() => {
      if (!enabled) {
         setText(prefix);
         return;
      }

      const fullPrompt =
         prompts[index % prompts.length]
            ?.split(' ')
            .slice(0, maxWords)
            .join(' ') + '...';

      const fullText = prefix + fullPrompt;

      const handleTyping = () => {
         if (isDeleting) {
            setText((prev) =>
               prev.length > prefix.length ? prev.slice(0, -1) : prefix,
            );

            if (text === prefix) {
               setIsDeleting(false);
               setIndex((prev) => (prev + 1) % prompts.length);
            }
         } else {
            setText(fullText.slice(0, text.length + 1));

            if (text === fullText) {
               timeoutRef.current = setTimeout(
                  () => setIsDeleting(true),
                  pauseDuration,
               );
               return;
            }
         }

         timeoutRef.current = setTimeout(
            handleTyping,
            isDeleting ? deletingSpeed : typingSpeed,
         );
      };

      timeoutRef.current = setTimeout(handleTyping, typingSpeed);

      return () => {
         if (timeoutRef.current) clearTimeout(timeoutRef.current);
      };
   }, [text, isDeleting, index, enabled, prompts, prefix]);

   return text;
};
