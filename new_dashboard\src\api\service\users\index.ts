import dashboardApiAgent from '../../agent';
import { PromiseAxios } from '../common';

export interface DefaultResponse {
   status: 'Success' | 'Failure';
   message: string;
}

/** DETAILS **/
export interface AllUserDetails {
   agency_name: string | null;
   agency_url: string | null;
   client_id: string;
   company_business_type: string;
   company_country: string;
   company_name: string;
   company_platform: string;
   company_traffic: string;
   company_url: string;
   country: string;
   email_address: string;
   profile_image: string;
   full_name: string;
   language: string;
   organization_type: 'Individual Business' | 'Marketing Agency';
   product_register_progress: string;
   test_connection_succeeded_timestamp: string | null;
   login_user_active: 'Y' | 'N';
   user_role: 'Admin' | 'Contributor';
   role_is_active: boolean;
   last_change_date: string;
   create_date: string;
   cb_product_updates: 'Y' | 'N';
   user_active: 'Y' | 'N';
   is_active: 'Y' | 'N';
   user_id: string;
}

/** PAYLOADS **/
export interface GetAllUsersPayload {
   email_address: string;
}

export interface CreateUpdateUserPayload {
   client_id: string;
   email_address: string;
   company_url: string;
   is_active: boolean;
   cb_product_updates: 'Y' | 'N';
   country: string;
   create_date: string;
   full_name: string;
   language: string;
   last_update_date: string;
   user_active: string;
   user_role: 'Admin' | 'Contributor';
   user_confirmed?: 'Y' | 'N';
   password?: string;
   confirm_password?: string;
   profile_image: string;
}

export interface DeleteUserPayload {
   email_address: string;
   company_url: string;
}

/** RESPONSES **/
export interface GetAllProfilesResponse extends DefaultResponse {
   details: AllUserDetails[];
}

export interface GetAllUsersResponse extends DefaultResponse {
   details: AllUserDetails[];
}

export interface CreateUpdateUserResponse extends DefaultResponse {}

export interface DeleteUserResponse extends DefaultResponse {}

interface Endpoints {
   getAllProfiles: (payload: {
      email_address: string;
   }) => PromiseAxios<GetAllProfilesResponse>;
   getAllUsers: (payload: {
      company_url: string;
   }) => PromiseAxios<GetAllUsersResponse>;

   createUser: (
      payload: CreateUpdateUserPayload,
   ) => PromiseAxios<CreateUpdateUserResponse>;

   updateUser: (
      payload: CreateUpdateUserPayload,
   ) => PromiseAxios<CreateUpdateUserResponse>;

   deleteUser: (payload: DeleteUserPayload) => PromiseAxios<DeleteUserResponse>;
}

const userManagementEndpoints: Endpoints = {
   getAllProfiles: (payload) =>
      dashboardApiAgent.get('/user/profiles', {
         params: { email_address: payload.email_address },
      }),
   getAllUsers: (payload) =>
      dashboardApiAgent.get('/user/all', {
         params: { company_url: payload.company_url },
      }),

   createUser: (payload) => dashboardApiAgent.post('/user', payload),

   updateUser: (payload) => dashboardApiAgent.put('/user', payload),

   deleteUser: (payload) =>
      dashboardApiAgent.delete('/user', { data: payload }),
};

export default userManagementEndpoints;
