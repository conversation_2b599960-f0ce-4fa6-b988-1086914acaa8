import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { useEffect, useState } from 'react';

interface Props {
   name: string;
   values: string;
   excludes: string;
   match_type: 'strict' | 'flexible';
   onChange: (field: string, value: string) => void;
   onRemove: () => void;
}

const EntityFields = (props: Props) => {
   const { name, values, excludes, match_type, onChange, onRemove } = props;

   const [local, setLocal] = useState({
      column: '',
      values: '',
      excludes: '',
      match_type: 'strict',
   });

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setLocal((prev) => ({ ...prev, [name]: value }));
      onChange(name, value);
   };

   const handleSelectChange = (value: 'strict' | 'flexible') => {
      setLocal((prev) => ({ ...prev, match_type: value }));
      onChange('match_type', value);
   };

   useEffect(() => {
      setLocal({
         column: name,
         values,
         excludes,
         match_type,
      });
   }, []);

   return (
      <div className='w-full grid grid-cols-1 md:grid-cols-9 gap-4 mb-4'>
         <div className='flex flex-col gap-1 col-span-2'>
            <Label
               htmlFor='column'
               className='text-[14px] ml-1 font-semibold gap-1'
            >
               Name<span className='text-red-500'>*</span>
            </Label>
            <Input
               className='h-[40px] border shadow-none focus-visible:ring-0 focus-visible:outline-none'
               id='column'
               type='text'
               name='column'
               value={local.column}
               onChange={handleInputChange}
            />
         </div>

         <div className='flex flex-col gap-1 col-span-2'>
            <Label
               htmlFor='values'
               className='text-[14px] ml-1 font-semibold gap-1'
            >
               Values<span className='text-red-500'>*</span>
            </Label>
            <Input
               className='h-[40px] border shadow-none focus-visible:ring-0 focus-visible:outline-none'
               id='values'
               type='text'
               name='values'
               value={local.values}
               placeholder='Enter comma separated values'
               onChange={handleInputChange}
            />
         </div>

         <div className='flex flex-col gap-1 col-span-2'>
            <Label
               htmlFor='excludes'
               className='text-[14px] ml-1 font-semibold gap-1'
            >
               Excludes
            </Label>
            <Input
               className='h-[40px] border shadow-none focus-visible:ring-0 focus-visible:outline-none'
               id='excludes'
               type='text'
               name='excludes'
               value={local.excludes}
               placeholder='Enter comma separated values'
               onChange={handleInputChange}
            />
         </div>

         <div className='flex flex-col gap-1 col-span-2'>
            <Label
               htmlFor='match_type'
               className='text-[14px] ml-1 font-semibold gap-1'
            >
               Match Type<span className='text-red-500'>*</span>
            </Label>
            <Select
               value={local.match_type}
               onValueChange={(val) =>
                  handleSelectChange(val as 'strict' | 'flexible')
               }
            >
               <SelectTrigger className='w-full !h-[40px] cursor-pointer focus-visible:ring-0 focus-visible:outline-none'>
                  <SelectValue placeholder='Select Match Type' />
               </SelectTrigger>
               <SelectContent className='bg-white'>
                  <SelectItem
                     key='flexible'
                     value='flexible'
                     className='cursor-pointer'
                  >
                     Flexible
                  </SelectItem>
                  <SelectItem
                     key='strict'
                     value='strict'
                     className='cursor-pointer'
                  >
                     Strict
                  </SelectItem>
               </SelectContent>
            </Select>
         </div>

         <div className='flex flex-col gap-1'>
            <Button onClick={onRemove} className='md:mt-6 bg-[#7F56D9]'>
               Delete
            </Button>
         </div>
      </div>
   );
};

export default EntityFields;
