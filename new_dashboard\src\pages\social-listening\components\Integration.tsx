import React from 'react';
import { IoWarningOutline } from 'react-icons/io5';
import { Icon } from '@chakra-ui/react';
import { OptimisationStatusKeys } from '../../../layouts/app-layout';

interface IntegrationProps {
   components: React.FC[];
   title: string;
   id?: string;
   completed: boolean;
   componentKey: OptimisationStatusKeys;
}

const info = {
   channels_marketplace: 'Please add atleast one channel or one marketplace',
   ads_account: 'Please add atleast one ads account',
   shipping_logistics: 'Please add atleast one shipping and logistics',
   seo: 'Please add atleast one seo or analytics',
};

const Integration: React.FC<IntegrationProps> = ({
   components,
   title,
   id,
   completed,
   componentKey,
}) => {
   return (
      <div className='social-media'>
         <div className='title'>
            <h5 id={id}>{title}</h5>
            {!completed && (
               <div className='info'>
                  <Icon as={IoWarningOutline} />
                  <span>{info[componentKey as keyof typeof info]}</span>
               </div>
            )}
         </div>
         <div className='social-media__components'>
            {components.map((Component, index) => (
               <Component key={index} />
            ))}
         </div>
      </div>
   );
};

export default Integration;
