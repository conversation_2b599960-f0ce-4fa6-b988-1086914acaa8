import { Flex, <PERSON>ack, Text, Button } from '@chakra-ui/react';

import React from 'react';

import { useAppDispatch } from '../../../../store/store';
import { useToast } from '@chakra-ui/react';
import { useEffect, useCallback, useMemo } from 'react';
import { useAppSelector } from '../../../../store/store';
import {
   //setChatLevel,
   setCurrentChat,
   updateAdCreativeCardState,
} from '../../../../store/reducer/meta-ads-manager-reducer';
import { ExtendedAdCreativePayload } from '../../agents/meta-ads-manager';
import { uploadImageToAzure } from '../../../../utils/imageUpload';
import { Keys, LocalStorageService } from '../../../../utils/local-storage';
import { getUsername } from '../../utils/meta-ads-manager-agent/get-username';
import metaAdsManagerEndPoints, {
   AgeData,
   AdCreativePayload,
   CreateAdPayload,
   AdSettings,
} from '../../../../api/service/agentic-workflow/meta-ads-manager';
import { useApiMutation } from '../../../../hooks/react-query-hooks';
import { AnalysisTable } from './analysis-table';
//import { RecommendationCard } from './recommendation-card';
import { PreviewCard } from '../../utils/meta-ads-manager-agent/preview-card';

/*const {
  adsetId,
      currentChat,
      chatLevel,
      campaignDetails,
      adsetData
   } = useAppSelector((state) => state.metaAdsManager);*/

export const AdCreativeCard = ({ messageIndex }: { messageIndex: number }) => {
   const [fileName, setFileName] = React.useState<string>('');
   const client_id = LocalStorageService.getItem(Keys.ClientId) as string;

   const createAdService = useApiMutation({
      queryKey: ['create-ad'],
      mutationFn: metaAdsManagerEndPoints.createAd,
   });
   const createAdCreativeService = useApiMutation({
      queryKey: ['create-adcreative'],
      mutationFn: metaAdsManagerEndPoints.createAdCreative,
   });
   const saveHistoryService = useApiMutation({
      queryKey: ['save-history'],
      mutationFn: metaAdsManagerEndPoints.saveHistory,
   });

   const username = getUsername();
   const dispatch = useAppDispatch();
   const toast = useToast();
   const adCreativeCardState = useAppSelector(
      (state) => state.metaAdsManager.adCreativeCardState,
   );

   const { adsetId, currentChat, chatLevel, campaignDetails, adsetData } =
      useAppSelector((state) => state.metaAdsManager);

   const message = currentChat?.[messageIndex];
   if (!message?.adCreativeData) return null;

   const isLastAdCreativeCard = currentChat
      ?.slice(messageIndex + 1)
      .every((msg) => !msg.adCreativeData);
   if (!isLastAdCreativeCard) return null;

   const adCreativeData = message.adCreativeData as ExtendedAdCreativePayload;
   const {
      name,
      object_story_spec,
      analysisData,
      recommendation,
      description,
   } = adCreativeData;
   const {
      message: caption,
      picture,
      call_to_action,
   } = object_story_spec.link_data;

   useEffect(() => {
      if (
         adCreativeCardState.isEditing &&
         adCreativeCardState.editedMessage === ''
      ) {
         dispatch(updateAdCreativeCardState({ editedMessage: caption }));
      }
   }, [
      adCreativeCardState.isEditing,
      adCreativeCardState.editedMessage,
      caption,
      dispatch,
   ]);

   const handleImageChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         const file = e.target.files?.[0];
         if (file) {
            setFileName(file.name);

            dispatch(updateAdCreativeCardState({ selectedImage: file }));

            const reader = new FileReader();
            reader.onloadend = () => {
               dispatch(
                  updateAdCreativeCardState({
                     previewImage: reader.result as string,
                  }),
               );
            };
            reader.readAsDataURL(file);
         }
      },
      [dispatch],
   );

   const memoizedObjectStorySpec = useMemo(
      () => object_story_spec,
      [object_story_spec],
   );
   //const memoizedMessage = useMemo(() => message, [message]);

   const handleSave = useCallback(async () => {
      try {
         if (adCreativeCardState.selectedImage) {
            dispatch(updateAdCreativeCardState({ isUploading: true }));

            const result = await uploadImageToAzure(
               adCreativeCardState.selectedImage,
            );

            dispatch(updateAdCreativeCardState({ isUploading: false }));

            if (
               result.success &&
               result.uri !== adCreativeCardState.uploadedImageUri
            ) {
               dispatch(
                  updateAdCreativeCardState({
                     uploadedImageUri: result.uri,
                     previewImage: result.uri,
                  }),
               );

               const updatedAdCreative = {
                  client_id,
                  name: username,
                  object_story_spec: {
                     ...object_story_spec,
                     link_data: {
                        ...object_story_spec.link_data,
                        message: adCreativeCardState.editedMessage,
                        picture: result.uri,
                     },
                  },
                  analysisData,
                  recommendation,
                  description,
               };

               const updatedChat = (currentChat || []).map((chat, index) =>
                  index === messageIndex
                     ? { ...chat, adCreativeData: updatedAdCreative }
                     : chat,
               );

               dispatch(setCurrentChat(updatedChat));
            }
         } else if (adCreativeCardState.editedMessage !== caption) {
            const updatedAdCreative = {
               client_id,
               name: username,
               object_story_spec: {
                  ...object_story_spec,
                  link_data: {
                     ...object_story_spec.link_data,
                     message: adCreativeCardState.editedMessage,
                  },
               },
               analysisData,
               recommendation,
               description,
            };

            const updatedChat = (currentChat || []).map((chat, index) =>
               index === messageIndex
                  ? { ...chat, adCreativeData: updatedAdCreative }
                  : chat,
            );

            dispatch(setCurrentChat(updatedChat));
         }

         dispatch(updateAdCreativeCardState({ isEditing: false }));
      } catch (error) {
         console.error('Error in handleSave:', error);
      }
   }, [
      adCreativeCardState.selectedImage,
      adCreativeCardState.editedMessage,
      adCreativeCardState.uploadedImageUri,
      caption,
      username,
      memoizedObjectStorySpec,
      analysisData,
      recommendation,
      description,
      dispatch,
      currentChat,
      messageIndex,
   ]);

   const formatAgeRange = (ageData: AgeData[] | undefined) => {
      if (!ageData || ageData.length === 0) return '18-65+';
      return ageData
         .filter((age) => age.age_range)
         .map((age) => age.age_range)
         .join(', ');
   };

   const createAdCreativeAndAd = async (status: 'ACTIVE' | 'PAUSED') => {
      try {
         if (!adsetId) {
            toast({
               title: 'Error',
               description: 'No adset ID found. Please create an adset first.',
               status: 'error',
               duration: 5000,
               isClosable: true,
            });
            return;
         }

         if (adCreativeCardState.adCreativeId || message.adSettings) {
            toast({
               title: 'Info',
               description: 'Ad creative already exists.',
               status: 'info',
               duration: 3000,
               isClosable: true,
            });
            return;
         }

         dispatch(
            updateAdCreativeCardState({
               isPublishing: status === 'ACTIVE',
               isSavingDraft: status === 'PAUSED',
            }),
         );

         const adCreativePayload: AdCreativePayload = {
            client_id,
            name: name,
            object_story_spec: {
               page_id: object_story_spec.page_id,
               link_data: {
                  link: object_story_spec.link_data.link,
                  name: object_story_spec.link_data.name,
                  message: adCreativeCardState.editedMessage || caption,
                  description: object_story_spec.link_data.description,
                  picture: adCreativeCardState.uploadedImageUri || picture,
                  call_to_action: object_story_spec.link_data.call_to_action,
               },
            },
         };

         let adCreativeResponse;
         let retryCount = 0;
         const maxRetries = 3;

         while (retryCount < maxRetries) {
            try {
               adCreativeResponse =
                  await createAdCreativeService.mutateAsync(adCreativePayload);
               if (adCreativeResponse?.id) break;
            } catch (error) {
               console.error(
                  `Ad creative creation attempt ${retryCount + 1} failed:`,
                  error,
               );
               retryCount++;
               if (retryCount === maxRetries) throw error;
               await new Promise((resolve) => setTimeout(resolve, 2000));
            }
         }

         if (!adCreativeResponse?.id) {
            throw new Error(
               'Failed to create ad creative after multiple attempts',
            );
         }

         const creativeId = adCreativeResponse.id;
         dispatch(updateAdCreativeCardState({ adCreativeId: creativeId }));

         const adPayload: CreateAdPayload = {
            client_id,
            name: `Ad_${Date.now()}`,
            adset_id: adsetId,
            creative: { creative_id: creativeId },
            status: status,
         };

         let adResponse;
         retryCount = 0;

         while (retryCount < maxRetries) {
            try {
               adResponse = await createAdService.mutateAsync(adPayload);
               if (adResponse?.id) break;
            } catch (error) {
               console.error(
                  `Ad creation attempt ${retryCount + 1} failed:`,
                  error,
               );
               retryCount++;
               if (retryCount === maxRetries) throw error;
               await new Promise((resolve) => setTimeout(resolve, 2000));
            }
         }

         if (!adResponse?.id) {
            throw new Error('Failed to create ad after multiple attempts');
         }

         const newAdId = adResponse.id;
         dispatch(updateAdCreativeCardState({ adId: newAdId }));

         const adSettingsData: AdSettings = {
            adCreativeId: creativeId,
            adId: newAdId,
            status: status,
            campaignName: campaignDetails?.name || 'N/A',
            adsetName: adsetData?.adset_name || 'N/A',
            dailyBudget: String(adsetData?.daily_budget || '10000'),
            locations: 'India',
            ageRange: formatAgeRange(adsetData?.targeting_analysis?.age),
            gender: 'Male ♂️, Female ♀️',
            optimizationGoal: adsetData?.optimization_goal || 'Purchase',
            adImage:
               adCreativeCardState.previewImage ||
               adCreativeCardState.uploadedImageUri ||
               picture,
         };

         const updatedChat = [...(currentChat || [])];
         updatedChat[messageIndex] = {
            ...message,
            adSettings: adSettingsData,
            adCreativeData: {
               ...adCreativeData,
               name,
               object_story_spec: {
                  ...object_story_spec,
                  link_data: {
                     ...object_story_spec.link_data,
                     message: adCreativeCardState.editedMessage || caption,
                     picture: adCreativeCardState.uploadedImageUri || picture,
                  },
               },
            },
         };
         dispatch(setCurrentChat(updatedChat));

         await saveHistoryService.mutateAsync({
            chat_name: 'Ad Creation',
            chat_history: JSON.stringify(updatedChat),
            chat_level: chatLevel,
            campaign_details: JSON.stringify(campaignDetails),
            adset_details: JSON.stringify(adsetData),
            ad_details: JSON.stringify({ id: newAdId, status }),
            ad_creative_details: JSON.stringify({
               ...adCreativePayload,
               analysisData,
               recommendation,
            }),
         });

         toast({
            title: 'Success',
            description:
               status === 'ACTIVE'
                  ? 'Ad published successfully🎉🎉'
                  : 'Ad saved as draft🎉🎉',
            status: 'success',
            duration: 5000,
            isClosable: true,
            position: 'bottom',
         });

         dispatch(
            updateAdCreativeCardState({
               isPublishing: false,
               isSavingDraft: false,
            }),
         );
      } catch (error) {
         console.error('Error creating ad creative and ad:', error);
         toast({
            title: 'Error',
            description:
               error instanceof Error ? error.message : 'Failed to create ad',
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
         dispatch(
            updateAdCreativeCardState({
               isPublishing: false,
               isSavingDraft: false,
            }),
         );
      }
   };

   const handleSaveAsDraft = async () => {
      await createAdCreativeAndAd('PAUSED');
      // dispatch(setChatLevel('completed'));
   };

   const handlePublish = async () => {
      await createAdCreativeAndAd('ACTIVE');
   };

   const isAdCreated = Boolean(
      adCreativeCardState.adCreativeId ||
         adCreativeCardState.adId ||
         message.adSettings,
   );

   return (
      <Stack spacing={6} width='100%' alignItems='flex-start'>
         <AnalysisTable
            analysisData={analysisData}
            recommendation={recommendation}
         />
         {/*<RecommendationCard recommendation={recommendation} />*/}

         <Flex width='100%' justifyContent='center'>
            <Stack spacing={6} width='100%' alignItems='center'>
               <PreviewCard
                  key={
                     adCreativeCardState.adCreativeId ||
                     'draft' ||
                     adCreativeCardState.uploadedImageUri
                  }
                  name={name || ''}
                  caption={caption || ''}
                  picture={picture || ''}
                  description={description || ''}
                  call_to_action={call_to_action || ''}
                  isEditing={!!(adCreativeCardState.isEditing && !isAdCreated)}
                  editedMessage={adCreativeCardState.editedMessage ?? ''}
                  setEditedMessage={(value) =>
                     dispatch(
                        updateAdCreativeCardState({ editedMessage: value }),
                     )
                  }
                  handleImageChange={handleImageChange}
                  handleSave={() => void handleSave()}
                  isUploading={adCreativeCardState.isUploading}
                  setIsEditing={(value) =>
                     dispatch(updateAdCreativeCardState({ isEditing: value }))
                  }
                  isExpanded={adCreativeCardState.isExpanded}
                  setIsExpanded={(value) =>
                     dispatch(updateAdCreativeCardState({ isExpanded: value }))
                  }
                  previewImage={adCreativeCardState.previewImage ?? ''}
                  uploadedImageUri={adCreativeCardState.uploadedImageUri ?? ''}
                  fileName={fileName}
                  link={object_story_spec.link_data.link}
               />

               {!isAdCreated ? (
                  <Flex
                     gap={4}
                     mt={4}
                     width='500px'
                     justifyContent='space-between'
                  >
                     <Button
                        flex='1'
                        onClick={() => void handleSaveAsDraft()}
                        isLoading={adCreativeCardState.isSavingDraft}
                        loadingText='Saving...'
                        isDisabled={isAdCreated}
                        maxW='220px'
                        ml={6}
                        height={14}
                        opacity={isAdCreated ? 0.5 : 1}
                        cursor={isAdCreated ? 'not-allowed' : 'pointer'}
                        borderColor='blue.500'
                        _hover={{
                           bg: 'gray', // light green on hover
                           borderColor: 'gray',
                           color: 'white',
                        }}
                        fontWeight='500'
                        fontSize='md'
                        colorScheme='black'
                        variant='outline'
                     >
                        Save as Draft
                     </Button>
                     <Button
                        flex='1'
                        colorScheme='blue'
                        _hover={{
                           bg: 'green.100', // light green on hover
                           borderColor: 'green.400',
                           color: 'green.600',
                        }}
                        mr={6}
                        height={14}
                        onClick={() => void handlePublish}
                        isLoading={adCreativeCardState.isPublishing}
                        loadingText='Publishing...'
                        isDisabled={isAdCreated}
                        maxW='220px'
                        opacity={isAdCreated ? 0.5 : 1}
                        cursor={isAdCreated ? 'not-allowed' : 'pointer'}
                     >
                        Publish
                     </Button>
                  </Flex>
               ) : (
                  <Flex gap={4} width='100%' justifyContent='center'>
                     <Text color='green.500' fontWeight='bold'>
                        {message.adSettings?.status === 'ACTIVE'
                           ? 'Ad Published Successfully'
                           : 'Ad Saved as Draft'}
                     </Text>
                  </Flex>
               )}
            </Stack>
         </Flex>
      </Stack>
   );
};
