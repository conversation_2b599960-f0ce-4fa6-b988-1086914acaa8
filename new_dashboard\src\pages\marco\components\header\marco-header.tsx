import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAppDispatch, useAppSelector } from '@/store/store';
import MarcoHistoryPopover from '../popovers/marco-history-popover';
import { setCurrentAgent, Agents } from '@/store/reducer/marco-reducer';
import { setCurrentSessionID as setAnalyticsSessionID } from '@/store/reducer/analytics-agent-reducer';

const AGENTS: { value: Agents; label: string }[] = [
   { value: 'analytics-agent', label: 'AI CMO' },
];

const MarcoHeader = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   const { currentAgent } = useAppSelector((state) => state.marco);

   const AGENT_RESET_ACTIONS: Record<Agents, () => void> = {
      'analytics-agent': () => dispatch(setAnalyticsSessionID('')),
   };

   const handleClickNewChat = () => {
      const resetAction = AGENT_RESET_ACTIONS[currentAgent];
      if (resetAction) resetAction();
   };

   return (
      <header className='flex mt-2 md:mt-4 w-full justify-between shrink-0 items-center gap-2 h-[52px]'>
         <div className='flex gap-2 mr-6'>
            <Select
               defaultValue={currentAgent}
               value={currentAgent}
               onValueChange={(value) => {
                  dispatch(setCurrentAgent(value as Agents));
                  navigate('/marco/' + value);
               }}
            >
               <SelectTrigger className='w-fit max-w-[220px] ml-2 text-[14px] md:text-[16px] text-black font-bold border-0 shadow-none hover:cursor-pointer'>
                  <SelectValue placeholder='Agent' />
               </SelectTrigger>
               <SelectContent className='bg-white'>
                  {AGENTS.map((agent) => (
                     <SelectItem
                        key={agent.value}
                        className='hover:cursor-pointer hover:bg-gray-50 font-semibold'
                        value={agent.value}
                     >
                        {agent.label}
                     </SelectItem>
                  ))}
               </SelectContent>
            </Select>
         </div>
         <div className='flex gap-2 mr-6'>
            <Button
               className='text-[12px] md:text-[16px] text-black  hover:cursor-pointer hover:bg-[#7F56D9] hover:text-white disabled:cursor-not-allowed'
               variant='outline'
               onClick={handleClickNewChat}
            >
               New Chat
            </Button>
            <MarcoHistoryPopover />
         </div>
      </header>
   );
};

export default MarcoHeader;
