import { addMilliseconds } from 'date-fns';
import {
   Connector,
   ConnectorKpiData,
   ConnectorKpiDataWithMeta,
   DateRange,
   KPIMeta,
   KPIRange,
   KPIStoreRange,
   KpiTimelineItem,
   MetaCatAgg,
   TimeRange,
   Anomalies,
   alertsAndOpportunities,
} from './interface';
import { defaultStaticRanges } from './default-ranges';
// import { TrackKPI } from '@/pages/pulse/components/interface';
import { calculateHelper } from '@/pages/utils/kpiCalculaterHelper';

export const filterVisibleKPI = (
   isPinned: boolean,
   catData: ConnectorKpiData,
   metaData: KPIMeta[],
   category: string,
   anomalies: Anomalies | undefined,
   //dispatch: AppDispatch,
): {
   filteredCat: ConnectorKpiDataWithMeta;
   filteredMeta: KPIMeta[];
   hiddenAnomalyAlerts: alertsAndOpportunities[];
} => {
   const filteredCat: ConnectorKpiDataWithMeta = {};
   const hiddenAnomalyAlerts: alertsAndOpportunities[] = [];

   const availableKPI = metaData.filter((kpi: KPIMeta) => {
      return isPinned ? kpi.pinned : kpi.visible;
   });
   const categoryFiltered = metaData.filter((kpi: KPIMeta) => {
      return kpi.category === category;
   });

   const filteredMeta = metaData.filter((kpi) => {
      if (kpi.category !== category) return true;
      return !!catData[kpi.kpi];
   });

   availableKPI.forEach((kpi) => {
      if (
         catData[kpi.kpi] &&
         catData[kpi.kpi].current_allData?.length > 0 &&
         catData[kpi.kpi].category == kpi.category
      ) {
         filteredCat[kpi.kpi] = {
            ...(catData[kpi.kpi] || kpi),
            visible: kpi.visible ?? true,
            pinned: kpi.pinned ?? true,
            order: isPinned
               ? (kpi.pinned_order ?? 999)
               : (kpi.visible_order ?? 999),
         };
      }
   });
   categoryFiltered.forEach((kpiMeta) => {
      const kpiName = kpiMeta.kpi;
      const hasAnomaly = anomalies?.[kpiName] ?? null;
      const kpiData = catData?.[kpiName];
      if (!kpiData) return;

      const { percentage, direction } = calculateHelper(
         kpiName,
         kpiData.current_total_value,
         kpiData.previous_total_value,
      );

      if (hasAnomaly !== null && !kpiMeta.visible && percentage) {
         hiddenAnomalyAlerts.push({
            kpiName: kpiData.kpi_display_name,
            direction: direction === 'is up' ? 'up' : 'down',
            isPositive: hasAnomaly === true,
            percentage,
            message: `${kpiData.kpi_display_name} is ${direction === 'is up' ? 'up' : 'down'} by ${percentage}%`,
            remove: false,
            category: kpiData.category,
         });
      } else if (hasAnomaly === null && !kpiMeta.visible) {
         hiddenAnomalyAlerts.push({
            kpiName: kpiData.kpi_display_name,
            percentage: percentage || '0',
            direction: direction === 'is up' ? 'up' : 'down',
            isPositive: hasAnomaly === true,
            message: `${kpiData.kpi_display_name} is ${direction === 'is up' ? 'up' : 'down'} by ${percentage}%`,
            remove: true,
            category: kpiData.category,
         });
      }
   });

   return {
      filteredCat,
      filteredMeta,
      hiddenAnomalyAlerts,
   };
};

export const getFullData = (
   dateRange: KPIStoreRange,
   data: KpiTimelineItem[],
   // groupBy: string,
): KpiTimelineItem[] => {
   // if (groupBy !== 'day') return data;

   const startDate = new Date(dateRange.start);
   const endDate = new Date(dateRange.end);

   const dateArray: KpiTimelineItem[] = [];
   let i = 0;

   while (startDate.getTime() <= endDate.getTime()) {
      const currData = data[i] || dateArray[0];
      const dataD = new Date(currData.date);

      if (
         dataD.getFullYear() === startDate.getFullYear() &&
         dataD.getMonth() === startDate.getMonth() &&
         dataD.getDate() === startDate.getDate()
      ) {
         dateArray.push(currData);
         i++;
      } else {
         dateArray.push({
            ...currData,
            date: `${startDate.getFullYear()}-${startDate.getMonth() + 1}-${startDate.getDate()}`,
            kpi_value: 0,
         });
      }

      startDate.setDate(startDate.getDate() + 1);
   }

   return dateArray;
};

export const getFormattedVal = (val: number) => {
   return val.toLocaleString('en-IN');
};

export const getLabel = (dateRange: TimeRange, noDefault?: boolean): string => {
   if (!noDefault) {
      const defaultLabel = defaultStaticRanges.filter((x) =>
         x.isSelected(dateRange),
      )[0]?.label;
      if (defaultLabel) return defaultLabel;
   }
   const isYearReq =
      !!(dateRange.endDate.getFullYear() - dateRange.startDate.getFullYear()) ||
      new Date().getFullYear() != dateRange.startDate.getFullYear();
   return `${getUserDate(dateRange.startDate, !!isYearReq)} - ${getUserDate(dateRange.endDate, !!isYearReq)}`;
};

export const getUserDate = (date: Date, isYearReq: boolean): string => {
   const month = date.toLocaleString('en-EN', { month: 'short' });
   const dateNum = date.getDate();
   const year = date.getFullYear();
   return `${month} ${dateNum}${isYearReq ? ', ' + year : ''}`;
};

export const toHHMMSS = (secs: number) => {
   const hours = Math.floor(secs / 3600);
   const minutes = Math.floor(secs / 60) % 60;
   const seconds = secs % 60;

   return [hours, minutes, seconds]
      .map((v) => (v < 10 ? '0' + v : v))
      .map((v) => v.toString().substring(0, 2))
      .join(':');
};

export const getValidGroupBy = (
   end: Date,
   start: Date,
   group: string,
): string => {
   const diffInTime = end.getTime() - start.getTime();
   const diffInDays = diffInTime / (1000 * 60 * 60 * 24);
   if (group == 'quarter') {
      if (diffInDays > 240) return 'quarter';
      else if (diffInDays > 60) return 'month';
      else if (diffInDays > 14) return 'week';
   } else if (group == 'month') {
      if (diffInDays > 60) return 'month';
      else if (diffInDays > 14) return 'week';
   } else if (group == 'week' && diffInDays > 14) return 'week';
   return 'day';
};

export const getPrevPeriod = (dateRange: KPIRange): KPIRange => {
   const timeDiff = dateRange.start.getTime() - dateRange.end.getTime();
   return {
      end: addMilliseconds(dateRange.end, timeDiff - 1000),
      start: addMilliseconds(dateRange.start, timeDiff),
   };
};

export const isDisabledGroupBy = (
   dateRange: DateRange,
   group: string,
): boolean => {
   const diffInTime =
      dateRange.endDate.getTime() - dateRange.startDate.getTime();
   const diffInDays = diffInTime / (1000 * 60 * 60 * 24);
   if (group == 'week') return diffInDays < 14;
   else if (group == 'month') return diffInDays < 60;
   else if (group == 'quarter') return diffInDays < 240;
   return false;
};

export const getMetricDescription = (key: string): string => {
   const descriptions: {
      [key: string]: string;
   } = {
      date_tim: 'Date',
      roas: 'ROAS',
      cpp: 'CPP',
      cpc: 'CPC',
      ctr: 'CTR',
      unique_ctr: 'Unique CTR',
      cpm: 'CPM',
      frequency: 'Ad Frequency',
      purchase: 'Purchase',
      clicks: 'Clicks',
      unique_clicks: 'Unique Clicks',
      reach: 'Reach',
      impressions: 'Impressions',
      spend: 'Spend',
      add_to_cart: 'Add to Cart',
      campaign_name: 'Campaign Name',
   };

   return descriptions[key] || key;
};

export const getChartDateLabel = (data: KpiTimelineItem[]) => {
   return data.map((x) => getUserDate(new Date(x.date.split(' ')[0]), false));
};

export const isValidDateString = (str: string): boolean => {
   const ymd = str.split('T')[0].split('-');
   if (ymd.length !== 3 || ymd.join('').length !== 8) return false;
   return new Date(str).toString() !== 'Invalid Date';
};

export const getAddedAvailable = (
   cat: string,
   kpiMeta: KPIMeta[],
   connectorData: Connector,
): { added: KPIMeta[]; available: KPIMeta[] } => {
   let added = [],
      available = [];
   if (cat == 'pinned') {
      added = kpiMeta
         .filter((kpi) => kpi.pinned)
         .sort((a, b) => a.pinned_order - b.pinned_order);
      available = kpiMeta
         .filter(
            (kpi) =>
               !kpi.pinned &&
               connectorData &&
               Object.keys(connectorData).includes(kpi.category),
         )
         .sort((a, b) => a.pinned_order - b.pinned_order);
   } else {
      added = kpiMeta
         .filter((kpi) => kpi.category == cat && kpi.visible)
         .sort((a, b) => a.visible_order - b.visible_order);
      available = kpiMeta
         .filter((kpi) => kpi.category == cat && !kpi.visible)
         .sort((a, b) => a.visible_order - b.visible_order);
   }
   return { added, available };
};

export const reorder = (
   list: KPIMeta[],
   startIndex: number,
   endIndex: number,
) => {
   const result = Array.from(list);
   const [removed] = result.splice(startIndex, 1);
   result.splice(endIndex, 0, removed);

   return result;
};

export const getAggData = (data: KPIMeta[]): MetaCatAgg => {
   const aggCategoryData = {} as MetaCatAgg;

   data.forEach((x) => {
      const cat = x.category.trim();
      if (aggCategoryData[cat]) {
         aggCategoryData[cat].push(x);
      } else {
         aggCategoryData[cat] = [x];
      }
   });
   return aggCategoryData;
};

export const getFullMonth = (num: string): string => {
   const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
   ];
   return monthNames[Number(num) - 1] || '';
};

export const getUserTime = (str: string): string => {
   const [hr, sec] = str.split(':').map(Number);
   return `${hr % 12 < 10 ? '0' : ''}${hr != 12 ? hr % 12 : 12}: ${sec < 10 ? '0' : ''}${sec} ${hr >= 12 ? 'PM' : 'AM'}`;
};
