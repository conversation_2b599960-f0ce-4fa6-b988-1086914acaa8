import dashboardApiAgent from '../../agent';
import { PromiseAxios } from '../common';

export interface Conditions {
   column: string;
   values: string[];
   match_type: 'strict' | 'flexible';
   excludes: string[];
}

/** PAYLOADS **/
interface FetchCustomDashboardSettingsPayload {
   client_id: string;
   user_id: string;
   channel: string;
}

interface UpdateCustomDashboardSettingsPayload {
   client_id: string;
   user_id: string;
   channel: string;
   settings: {
      campaign: Record<string, Conditions>;
      adset: Record<string, Conditions>;
   };
}

interface FetchCustomDashboardDataPayload {
   client_id: string;
   user_id: string;
   channel: string;
   start_date: string;
   end_date: string;
}

/** RESPONSES **/

interface FetchCustomDashboardSettingsResponse {
   client_id: string;
   user_id: string;
   channel_name: string;
   campaign: Record<string, Conditions>;
   adset: Record<string, Conditions>;
   created_at: string;
   updated_at: string;
}

export interface FetchCustomDashboardDataResponse {
   [key: string]: string;
   region: string;
   objective: string;
   product: string;
   no_of_ads: string;
   spend: string;
   reach: string;
   impressions: string;
   clicks: string;
   leads: string;
   cpm: string;
   cpl: string;
   ctr: string;
   frequency: string;
}

/** ENDPOINTS **/
interface Endpoints {
   getCustomDashboardSettings: (
      payload: FetchCustomDashboardSettingsPayload,
   ) => PromiseAxios<FetchCustomDashboardSettingsResponse[]>;

   updateCustomDashboardSettings: (
      payload: UpdateCustomDashboardSettingsPayload,
   ) => PromiseAxios<FetchCustomDashboardSettingsResponse[]>;

   getCustomDashboardData: (
      payload: FetchCustomDashboardDataPayload,
   ) => PromiseAxios<FetchCustomDashboardDataResponse[]>;
}

const customDashboardAPI: Endpoints = {
   getCustomDashboardSettings: (payload) => {
      return dashboardApiAgent.get(
         `/custom-dashboard/${payload.client_id}/${payload.user_id}/${payload.channel}/settings`,
      );
   },

   updateCustomDashboardSettings: (payload) => {
      return dashboardApiAgent.post(
         `/custom-dashboard/${payload.client_id}/${payload.user_id}/${payload.channel}/settings`,
         payload,
      );
   },

   getCustomDashboardData: (payload) => {
      return dashboardApiAgent.post(
         `/custom-dashboard/${payload.client_id}/${payload.user_id}/${payload.channel}/data`,
         payload,
      );
   },
};

export default customDashboardAPI;
