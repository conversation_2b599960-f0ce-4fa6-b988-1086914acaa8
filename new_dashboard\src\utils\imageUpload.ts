import socialWatchEndpoints from '../api/service/social-watch/apis';

export interface ImageUploadResponse {
   uri: string;
   success: boolean;
   error?: string;
}

export const uploadImageToAzure = async (
   imageFile: File,
): Promise<ImageUploadResponse> => {
   try {
      const formData = new FormData();
      formData.append('media', imageFile);

      const uploadResponse =
         await socialWatchEndpoints.uploadMediaToAzure(formData);

      if (!uploadResponse?.data?.uri) {
         throw new Error('No URI received from upload');
      }

      const decodeResponse = await socialWatchEndpoints.decodeAzureMediaUrl({
         image_url: uploadResponse.data.uri,
      });

      if (!decodeResponse?.data?.uri) {
         throw new Error('Failed to decode image URL');
      }
      return {
         uri: decodeResponse.data.uri,
         success: true,
      };
   } catch (error) {
      console.error('Image upload error:', error);
      return {
         uri: '',
         success: false,
         error:
            error instanceof Error ? error.message : 'Failed to upload image',
      };
   }
};
