import { AuthUser } from '../types/auth';

type LocalStoragePayload = Record<string, string | boolean | number | object>;

enum Keys {
   FlableUserDetails = 'userDetails',
   TwitUser = 'twitter-user',
   Token = 'token',
   UserName = 'userName',
   ClientId = 'clientId',
}

class LocalStorageService {
   static getItem<T>(key: Keys): T | null {
      const item = localStorage.getItem(key);
      if (item) {
         try {
            if (item.startsWith('{')) return JSON.parse(item) as T;
            return item as T;
         } catch (error) {
            console.error(`Error parsing JSON for key "${key}":`, error);
            return null;
         }
      }
      return null;
   }

   static setItem(
      key: Keys,
      value: string | number | boolean | AuthUser | LocalStoragePayload,
   ): void {
      try {
         const item =
            typeof value === 'object'
               ? JSON.stringify(value)
               : value.toString();
         localStorage.setItem(key, item);
      } catch (error) {
         console.error(`Error stringifying value for key "${key}":`, error);
      }
   }

   static removeItem(key: Keys): void {
      localStorage.removeItem(key);
   }
   static clearAll(): void {
      localStorage.clear();
   }
   static removeKeys(startWith: string) {
      Object.keys(localStorage).forEach((key) => {
         if (key.startsWith(startWith)) {
            localStorage.removeItem(key);
         }
      });
   }
}

export { LocalStorageService, Keys };
