import { Button } from '@/components/ui/button';
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from '@/components/ui/popover';
import {
   Accordion,
   AccordionContent,
   AccordionItem,
   AccordionTrigger,
} from '@/components/ui/accordion';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { LuHistory } from 'react-icons/lu';
import { groupAgentChatsByDate } from '../../utils/analytics-agent/helpers';
import {
   setCurrentSessionID as setAnalyticsSessionID,
   setCurrentPage as setAnalyticsCurrentPage,
} from '@/store/reducer/analytics-agent-reducer';
import { Spinner } from '@chakra-ui/react';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { useFetchHistoryQuery } from '../../apis/analytics-agent-apis';
import { setCurrentHistory } from '@/store/reducer/marco-reducer';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/utils';

const MODES = {
   cmo: 'Deep Analysis',
};

const MarcoHistoryPopover = () => {
   const dispatch = useAppDispatch();

   const [searchTerm, setSearchTerm] = useState<string>('');

   const { currentAgent, currentHistory } = useAppSelector(
      (state) => state.marco,
   );
   const {
      currentPage: analyticsCurrentPage,
      runningChats,
      currentSessionID,
   } = useAppSelector((state) => state.analyticsAgent);

   const {
      data: analyticsHistoryData,
      isLoading: isAnalyticsHistoryLoading,
      refetch: refetchAnalyticsHistory,
   } = useFetchHistoryQuery();

   const groupLabels: { [key: string]: string } = {
      today: 'Today',
      yesterday: 'Yesterday',
      last7Days: 'Last 7 Days',
      thisMonth: 'This Month',
      lastMonth: 'Last Month',
      older: 'Older',
   };

   const renderAnalyticsHistory = () => {
      if (
         !currentHistory['analytics-agent'] ||
         currentHistory['analytics-agent'].length === 0
      ) {
         return (
            <div className='text-center text-gray-500'>
               No history available
            </div>
         );
      }

      const groupedData = groupAgentChatsByDate(
         currentHistory['analytics-agent'].filter(
            (chat) =>
               chat.user_query
                  .toLowerCase()
                  .includes(searchTerm.toLowerCase()) ||
               (chat?.session_names &&
                  chat?.session_names.length > 0 &&
                  chat?.session_names[0]
                     .toLowerCase()
                     .includes(searchTerm.toLowerCase())),
         ),
      );

      const refetchingAnalyticsHistory = async () => {
         dispatch(setAnalyticsCurrentPage(analyticsCurrentPage + 1));
         await refetchAnalyticsHistory();
      };

      const openSelectedSession = (sessionId: string) => {
         dispatch(setAnalyticsSessionID(sessionId));
      };

      return (
         <Accordion type='multiple' value={Object.keys(groupedData)}>
            {Object.entries(groupedData).map(([key, chats]) => {
               if (chats.length === 0) return null;

               return (
                  <AccordionItem value={key} key={key}>
                     <AccordionTrigger className='text-lg font-bold text-gray-800 mb-2'>
                        {groupLabels[key]}
                     </AccordionTrigger>
                     <AccordionContent>
                        {chats.map((chat) => (
                           <div
                              key={chat.chat_id}
                              onClick={() =>
                                 openSelectedSession(chat.session_id)
                              }
                              className={cn(
                                 'border-b last:border-b-0 px-2 py-3 hover:bg-gray-50 hover:cursor-pointer rounded-sm',
                                 currentSessionID === chat.session_id &&
                                    'bg-gray-100',
                              )}
                           >
                              <p className='text-sm text-gray-700 font-semibold'>
                                 {chat?.session_names?.[0] ||
                                    (chat.user_query.length > 60
                                       ? `${chat.user_query.slice(0, 60)}...`
                                       : chat.user_query) ||
                                    'No message'}
                              </p>
                              <div
                                 className={cn(
                                    'flex items-center justify-between mt-3 gap-1',
                                 )}
                              >
                                 {Array.isArray(chat.question_modes) &&
                                    chat.question_modes.length > 0 &&
                                    chat.question_modes
                                       .filter(
                                          (mode): mode is string =>
                                             typeof mode === 'string' &&
                                             mode.trim() !== '',
                                       )
                                       .map((mode) => mode.trim())
                                       .includes('cmo') && (
                                       <div className='flex items-center gap-1 w-[35%]'>
                                          <Badge className='text-xs text-black px-2 py-0 bg-white font-semibold rounded-full'>
                                             {MODES[
                                                'cmo' as keyof typeof MODES
                                             ] || 'Deep Analysis'}
                                          </Badge>
                                       </div>
                                    )}

                                 <div className='flex items-center gap-1 w-[50%]'>
                                    {chat.chat_ids &&
                                       chat.chat_ids.length > 0 &&
                                       chat.chat_ids
                                          .filter(
                                             (id) => id && id.trim() !== '',
                                          )
                                          .map((id) => {
                                             if (
                                                runningChats.includes(id) &&
                                                currentSessionID !==
                                                   chat.session_id
                                             )
                                                return (
                                                   <div className='flex justify-between items-center gap-2 border-2 px-2 py-1 rounded-sm'>
                                                      <span className='text-xs text-green-800'>
                                                         Analysis Running
                                                      </span>
                                                      <Spinner
                                                         size='xs'
                                                         color='green'
                                                      />
                                                   </div>
                                                );
                                          })}
                                 </div>
                                 <p className='mt-2 text-right text-[11px] text-gray-400'>
                                    {format(
                                       new Date(chat.updated_at),
                                       'MMM dd hh:mma',
                                    )}
                                 </p>
                              </div>
                           </div>
                        ))}
                     </AccordionContent>
                  </AccordionItem>
               );
            })}
            {isAnalyticsHistoryLoading ? (
               <div className='flex h-full items-center justify-center'>
                  <Spinner />
               </div>
            ) : (
               <Button
                  className='w-full'
                  variant='outline'
                  onClick={() => void refetchingAnalyticsHistory()}
               >
                  Load More
               </Button>
            )}
         </Accordion>
      );
   };

   const renderHistoryByAgent = () => {
      if (currentAgent === 'analytics-agent') {
         return currentHistory['analytics-agent']?.length ? (
            <div className='h-[calc(100vh-150px)] overflow-auto pb-[300px]'>
               {renderAnalyticsHistory()}
            </div>
         ) : (
            noHistoryUI()
         );
      }
      return null;
   };

   const noHistoryUI = () => (
      <div className='h-[calc(100vh-150px)] overflow-auto'>
         <div className='text-center text-gray-500'>
            No history available. Start chatting to see history.
         </div>
      </div>
   );

   useEffect(() => {
      if (
         !isAnalyticsHistoryLoading &&
         analyticsHistoryData &&
         analyticsHistoryData.length > 0
      ) {
         dispatch(
            setCurrentHistory({
               agent: 'analytics-agent',
               history: analyticsHistoryData,
            }),
         );
      }
   }, [isAnalyticsHistoryLoading]);

   useEffect(() => {
      setSearchTerm('');
   }, [currentAgent]);

   return (
      <Popover>
         <PopoverTrigger>
            <Button className='hover:cursor-pointer' variant='outline'>
               <LuHistory />
            </Button>
         </PopoverTrigger>
         <PopoverContent className='w-[300px] sm:w-[370px] h-[calc(100vh-150px)] mr-2 overflow-hidden bg-white'>
            <div className='grid gap-4'>
               <div className='space-y-2'>
                  <p className='text-xl font-bold'>History</p>
               </div>
               <div className='space-y-2'>
                  <Input
                     placeholder='Search history...'
                     value={searchTerm}
                     onChange={(e) => setSearchTerm(e.target.value)}
                  />
               </div>
               {renderHistoryByAgent()}
            </div>
         </PopoverContent>
      </Popover>
   );
};

export default MarcoHistoryPopover;
