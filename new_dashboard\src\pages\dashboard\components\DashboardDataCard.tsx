import { Box } from '@chakra-ui/react';
import { openModal } from '@/store/reducer/modal-reducer';
import { modalTypes } from '@/components/modals/modal-types';
import { useDispatch } from 'react-redux';
import { GrCircleQuestion } from 'react-icons/gr';
import { FiPlusCircle } from 'react-icons/fi';
import KPICard from './KpiCard';
import {
   DashboardDataCardProps,
   KpiDataWithMeta,
   KPIMeta,
} from '../utils/interface';
import { BsPinFill } from 'react-icons/bs';
import { filterVisibleKPI } from '../utils/helpers';
import KPIImage from './kpi-image';
import TooltipContent from './tooltip-content';

import OverallPerformanceCard from './OverallPerformanceCard';
import AlertsOpportunitiesCard from './AlertsOpportunitiesCard';
import { useEffect } from 'react';
import {
   setAlertsAndOpportunities,
   removeAlertOrOpportunity,
} from '@/store/reducer/kpi-reducer';
import PerformanceAndAlertsSkeleton from '../utils/PerformanceAndAlertsSkeleton';
const DashboardDataCard = ({
   keyValue,
   label,
   data,
   connectorKey,
   metaData,
   anomalies,
   allConnectorsLoaded,
}: DashboardDataCardProps) => {
   const dispatch = useDispatch();
   const {
      filteredCat,
      filteredMeta,
      hiddenAnomalyAlerts /*hiddenResolvedAlerts,*/,
   } = filterVisibleKPI(
      keyValue === 'pinned',
      data[connectorKey],
      metaData as KPIMeta[],
      connectorKey,
      anomalies,
   );

   useEffect(() => {
      hiddenAnomalyAlerts.forEach((alert) => {
         if (alert.remove) {
            dispatch(removeAlertOrOpportunity(alert.kpiName));
         } else {
            dispatch(setAlertsAndOpportunities(alert));
         }
      });
   }, [JSON.stringify(hiddenAnomalyAlerts)]);
   const imageStyles: object = {
      height: keyValue == 'amazon_ads' ? '20px' : '25px',
      width: keyValue == 'amazon_ads' ? '30px' : '25px',
      position: keyValue == 'amazon_ads' ? 'relative' : '',
      top: keyValue == 'amazon_ads' ? '3px' : '',
   };

   const handleAdd = () => {
      dispatch(
         openModal({
            modalType: modalTypes.ADD_KPI_MODAL,
            modalProps: {
               metaData: filteredMeta,
               head: connectorKey,
            },
         }),
      );
   };

   if (
      keyValue !== 'pinned' &&
      (data[connectorKey] === undefined ||
         Object.keys(data[connectorKey]).length === 0 ||
         Object.keys(filteredCat).length === 0)
   ) {
      return (
         <Box>
            <div className='flex gap-2 items-center font-normal font-poppins mb-4 font-medium head5'>
               {keyValue === 'pinned' ? (
                  <BsPinFill />
               ) : (
                  <KPIImage kpiCat={keyValue} style={imageStyles} />
               )}{' '}
               {label}
               <TooltipContent category={connectorKey} hasArrow placement='top'>
                  <GrCircleQuestion />
               </TooltipContent>
               <TooltipContent label='Add more KPIs' hasArrow placement='top'>
                  <FiPlusCircle cursor={'pointer'} onClick={handleAdd} />{' '}
               </TooltipContent>
            </div>
            <p className='para5 font-normal text-charcoal pt-3'>
               No data available for selected date range.
            </p>
         </Box>
      );
   }

   return (
      <Box>
         <div className=' flex items-center gap-2 mb-4 font-medium font-normal font-poppins head5'>
            {keyValue === 'pinned' ? (
               <BsPinFill />
            ) : (
               <KPIImage kpiCat={keyValue} style={imageStyles} />
            )}{' '}
            {label}
            <TooltipContent category={connectorKey} hasArrow placement='top'>
               <GrCircleQuestion />
            </TooltipContent>
            <TooltipContent label='Add more KPIs' hasArrow placement='top'>
               <FiPlusCircle cursor={'pointer'} onClick={handleAdd} />{' '}
            </TooltipContent>
         </div>

         {keyValue === 'pinned' && (
            <Box display='flex' flexWrap='wrap' gap={4} mb={4}>
               {allConnectorsLoaded ? (
                  <>
                     <OverallPerformanceCard />
                     <AlertsOpportunitiesCard />
                  </>
               ) : (
                  <>
                     <PerformanceAndAlertsSkeleton />
                  </>
               )}
            </Box>
         )}
         <Box display='flex' flexWrap='wrap' gap={4}>
            {Object.values(filteredCat)
               .sort((a, b) => a.order - b.order)
               .map((kpi: KpiDataWithMeta) => (
                  <KPICard
                     key={kpi.kpi_names}
                     keyValue={keyValue}
                     kpiDetails={kpi}
                     anomaly={anomalies ? anomalies[kpi.kpi_names] : null}
                  />
               ))}
         </Box>
      </Box>
   );
};

export default DashboardDataCard;
