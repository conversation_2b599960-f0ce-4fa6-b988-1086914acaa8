import { Conditions } from '@/api/service/custom-dashboard';

interface DateAgg {
   [key: string]: KPIList;
}
interface KPIList {
   [key: string]: number;
}
interface ROAS {
   totalSpent: number;
   totalRevenue: number;
}
interface CPM {
   totalSpent: number;
   totalImpressions: number;
}
interface CTR {
   totalClicks: number;
   totalImpressions: number;
}
interface FREQUENCY {
   totalImpressions: number;
   totalReach: number;
}
interface CostPerLead {
   totalSpent: number;
   totalLeads: number;
}

export interface DataRow {
   [key: string]: string | number;
}

interface FilterConfig {
   campaign: Record<string, Conditions> | Record<string, Conditions[]>;
   adset: Record<string, Conditions> | Record<string, Conditions[]>;
}

/* ---------------- KPI helpers (safe returns) ---------------- */
const KPI_CALCULATE = {
   roas: (dateAgg: DateAgg): number => {
      const totals = Object.values(dateAgg).reduce(
         (acc: ROAS, kpis: KPIList) => {
            const spend = Number(kpis.spend || 0);
            const revenue = Number(kpis.revenue || 0); // ensure your KPIList has `revenue`
            return {
               totalSpent: acc.totalSpent + spend,
               totalRevenue: acc.totalRevenue + revenue,
            };
         },
         { totalSpent: 0, totalRevenue: 0 },
      );

      if (totals.totalSpent === 0) return 0;
      return totals.totalRevenue / totals.totalSpent;
   },
   cpm: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (acc: CPM, kpis: KPIList) => {
            return {
               totalSpent: acc.totalSpent + Number(kpis.spend || 0),
               totalImpressions:
                  acc.totalImpressions + Number(kpis.impressions || 0),
            };
         },
         { totalSpent: 0, totalImpressions: 0 },
      );
      if (sums.totalImpressions === 0) return 0;
      return (sums.totalSpent / sums.totalImpressions) * 1000;
   },
   cpl: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (acc: CostPerLead, kpis: KPIList) => {
            return {
               totalSpent: acc.totalSpent + Number(kpis.spend || 0),
               totalLeads: acc.totalLeads + Number(kpis.leads || 0),
            };
         },
         { totalSpent: 0, totalLeads: 0 },
      );
      if (sums.totalLeads === 0) return 0;
      return sums.totalSpent / sums.totalLeads;
   },
   ctr: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (acc: CTR, kpis: KPIList) => {
            return {
               totalClicks: acc.totalClicks + Number(kpis.clicks || 0),
               totalImpressions:
                  acc.totalImpressions + Number(kpis.impressions || 0),
            };
         },
         { totalClicks: 0, totalImpressions: 0 },
      );
      if (sums.totalImpressions === 0) return 0;
      return (sums.totalClicks / sums.totalImpressions) * 100;
   },
   frequency: (dateAgg: DateAgg): number => {
      const frequencySums = Object.values(dateAgg).reduce(
         (acc: FREQUENCY, kpis: KPIList) => {
            return {
               totalImpressions:
                  Number(acc.totalImpressions) + Number(kpis.impressions || 0),
               totalReach: Number(acc.totalReach) + Number(kpis.reach || 0),
            };
         },
         { totalImpressions: 0, totalReach: 0 },
      );
      if (frequencySums.totalReach === 0) return 0;
      return frequencySums.totalImpressions / frequencySums.totalReach;
   },
};

/* ---------------- Helpers ---------------- */
const cartesianProduct = (arrays: string[][]): string[][] => {
   if (arrays.length === 0) return [[]];
   return arrays.reduce<string[][]>(
      (acc, arr) =>
         acc
            .map((prefix) => arr.map((v) => [...prefix, v]))
            .reduce((a, b) => a.concat(b), []),
      [[]],
   );
};

export const aggregateKPIsByConfig = (
   data: DataRow[],
   config: FilterConfig,
   primaryKeyCols: string[],
) => {
   const results: Record<string, Record<string, string | number>> = {};

   const campaignCols = Object.keys(config.campaign || {});
   const adsetCols = Object.keys(config.adset || {});

   const campaignValuesLists = campaignCols.map((col) => {
      const values = new Set<string>();
      for (const row of data) {
         if (row[col] !== undefined && row[col] !== null)
            values.add(String(row[col]));
      }
      return Array.from(values);
   });

   const adsetValuesLists = adsetCols.map((col) => {
      const values = new Set<string>();
      for (const row of data) {
         if (row[col] !== undefined && row[col] !== null)
            values.add(String(row[col]));
      }
      return Array.from(values);
   });

   const campaignCombosRaw = cartesianProduct(campaignValuesLists);
   const adsetCombosRaw = cartesianProduct(adsetValuesLists);

   const campaignCombos =
      campaignCombosRaw.length > 0 ? campaignCombosRaw : [[]];
   const adsetCombos = adsetCombosRaw.length > 0 ? adsetCombosRaw : [[]];

   // robust numeric parser: strips commas and non-numeric symbols (keeps - and .)
   const parseNumeric = (v: unknown): number | null => {
      if (v === null || v === undefined) return null;
      if (typeof v === 'number') return Number.isFinite(v) ? v : null;
      const s = String(v).trim();
      if (s === '') return null;
      // remove commas and anything that's not digit, dot, minus
      const cleaned = s.replace(/,/g, '').replace(/[^\d.-]/g, '');
      if (cleaned === '' || cleaned === '-' || cleaned === '.') return null;
      const n = Number(cleaned);
      return Number.isFinite(n) ? n : null;
   };

   const asNumberSafe = (v: string | number | undefined | null): number => {
      const parsed = parseNumeric(v as unknown);
      return parsed === null ? 0 : parsed;
   };

   const pickNumeric = (
      obj: Record<string, string | number>,
      keys: string[],
   ): number => {
      for (const k of keys) {
         if (obj[k] !== undefined && obj[k] !== null) {
            const parsed = parseNumeric(obj[k]);
            if (parsed !== null) return parsed;
         }
      }
      return 0;
   };

   for (const campVals of campaignCombos) {
      const campaignCombo: Record<string, string> = {};
      campaignCols.forEach((col, idx) => {
         campaignCombo[col] = campVals[idx];
      });

      for (const adVals of adsetCombos) {
         const adsetCombo: Record<string, string> = {};
         adsetCols.forEach((col, idx) => {
            adsetCombo[col] = adVals[idx];
         });

         const matchingRows = data.filter((row) =>
            [
               ...campaignCols.map((c) =>
                  campaignCombo[c] !== undefined
                     ? row[c] === campaignCombo[c]
                     : true,
               ),
               ...adsetCols.map((c) =>
                  adsetCombo[c] !== undefined ? row[c] === adsetCombo[c] : true,
               ),
            ].every(Boolean),
         );

         if (!matchingRows.length) continue;

         const groupMap = new Map<string, DataRow[]>();

         for (const row of matchingRows) {
            const key = primaryKeyCols
               .map((col) => `${col}:${String(row[col] ?? '')}`)
               .join(';');
            if (!groupMap.has(key)) groupMap.set(key, []);
            groupMap.get(key)!.push(row);
         }

         for (const [key, subset] of groupMap.entries()) {
            if (!subset.length) continue;

            const dateAgg: DateAgg = {};

            for (const r of subset) {
               const dateKey = String(r.date ?? r.day ?? r.report_date ?? '');
               if (!dateKey) continue;

               if (!dateAgg[dateKey]) {
                  dateAgg[dateKey] = {
                     spend: 0,
                     impressions: 0,
                     clicks: 0,
                     leads: 0,
                     reach: 0,
                     revenue: 0,
                     __roasWeighted: 0,
                     __spendForRoas: 0,
                  } as KPIList;
               }

               const d = dateAgg[dateKey];

               const spend = pickNumeric(r, [
                  'spend',
                  'total_spent',
                  'spend_amount',
               ]);
               const impressions = pickNumeric(r, [
                  'impressions',
                  'total_impressions',
               ]);
               const clicks = pickNumeric(r, ['clicks', 'total_clicks']);
               const leads = pickNumeric(r, [
                  'leads',
                  'total_leads',
                  'form_leads',
               ]);
               const reach = pickNumeric(r, ['reach', 'total_reach', 'Reach']);
               const revenue = pickNumeric(r, [
                  'revenue',
                  'total_revenue',
                  'purchase_value',
               ]);

               d.spend += spend;
               d.impressions += impressions;
               d.clicks += clicks;
               d.leads += leads;
               d.reach += reach;
               d.revenue += revenue;
               d.__roasWeighted += revenue; // accumulate revenue for roas
               d.__spendForRoas += spend;
            }

            // compute per-date roas if needed (keeps dateAgg consistent)
            Object.values(dateAgg).forEach((o) => {
               o.roas =
                  o.__spendForRoas > 0
                     ? o.__roasWeighted / o.__spendForRoas
                     : 0;
               delete o.__roasWeighted;
               delete o.__spendForRoas;
            });

            // totals for the group (numeric)
            const total_spent = subset.reduce(
               (s, r) =>
                  s + pickNumeric(r, ['spend', 'total_spent', 'spend_amount']),
               0,
            );
            const total_clicks = subset.reduce(
               (s, r) => s + pickNumeric(r, ['clicks', 'total_clicks']),
               0,
            );
            const total_impressions = subset.reduce(
               (s, r) =>
                  s + pickNumeric(r, ['impressions', 'total_impressions']),
               0,
            );
            const total_leads = subset.reduce(
               (s, r) =>
                  s + pickNumeric(r, ['leads', 'total_leads', 'form_leads']),
               0,
            );
            const total_revenue = subset.reduce(
               (s, r) =>
                  s +
                  pickNumeric(r, [
                     'revenue',
                     'total_revenue',
                     'purchase_value',
                  ]),
               0,
            );

            const noOfAdsSum = new Set(
               subset.map((r) => r.ad_id).filter(Boolean),
            ).size;

            const round2 = (num: number) => Number((num || 0).toFixed(2));

            const roas =
               total_spent > 0 ? round2(total_revenue / total_spent) : 0;
            const cpm = round2(KPI_CALCULATE.cpm(dateAgg));
            const cpl = round2(KPI_CALCULATE.cpl(dateAgg));
            const ctr = round2(KPI_CALCULATE.ctr(dateAgg));
            const frequency = round2(KPI_CALCULATE.frequency(dateAgg));

            const currencyCode =
               (subset[0].currency as string | undefined) ?? null;

            // build numeric result object (do NOT format currency here)
            const resultObj: Record<string, string | number> = {};
            for (const col of primaryKeyCols) {
               resultObj[col] = subset[0][col];
            }

            // numeric fields
            resultObj.total_spent = total_spent;
            resultObj.total_clicks = total_clicks;
            resultObj.total_impressions = total_impressions;
            resultObj.leads = total_leads;
            resultObj.noOfAds = noOfAdsSum;
            resultObj.roas = roas;
            resultObj.cpm = cpm;
            resultObj.cpl = cpl;
            resultObj.ctr = ctr;
            resultObj.frequency = frequency;
            resultObj.revenue = total_revenue;

            // store currency code for later formatting/display
            resultObj._currency = currencyCode ?? '';

            // merge additive metrics if already exists (numeric merge)
            if (!results[key]) {
               results[key] = resultObj;
            } else {
               const existing = results[key];
               const additive = [
                  'total_spent',
                  'total_clicks',
                  'total_impressions',
                  'leads',
                  'noOfAds',
                  'revenue',
               ];
               for (const field of additive) {
                  existing[field] =
                     asNumberSafe(existing[field]) +
                     asNumberSafe(resultObj[field]);
               }
               // overwrite recalculated ratios
               existing.roas = roas;
               existing.cpm = cpm;
               existing.cpl = cpl;
               existing.ctr = ctr;
               existing.frequency = frequency;
               // keep existing._currency (assuming groups share currency) — if not, you may want to decide a rule
            }
         }
      }
   }

   // Keep numeric values; no string formatting
   for (const k of Object.keys(results)) {
      const row = results[k];
      const round2 = (n: number) => Number((n || 0).toFixed(2));

      row.total_spent = round2(asNumberSafe(row.total_spent));
      row.cpm = round2(asNumberSafe(row.cpm));
      row.cpl = round2(asNumberSafe(row.cpl));
      row.revenue = round2(asNumberSafe(row.revenue));
      row.ctr = round2(asNumberSafe(row.ctr));
      row.roas = round2(asNumberSafe(row.roas));
      row.frequency = round2(asNumberSafe(row.frequency));
   }

   return results;
};

export const HEADER_COLUMNS = {
   region: 'Region',
   product: 'Product Type',
   campaign_name: 'Campaign Name',
   adset_name: 'Adset Name',
   objective: 'Objective',
   total_spent: 'Amount Spent',
   total_clicks: 'Clicks',
   total_impressions: 'Impressions',
   leads: 'Leads',
   noOfAds: 'No of Ads',
   roas: 'ROAS',
   cpm: 'CPM',
   cpl: 'CPL',
   ctr: 'CTR',
   frequency: 'Frequency',
   revenue: 'Revenue',
};
