import { KPICategory } from '@/utils/strings/kpi-constants';
import { PlacementWithLogical } from '@chakra-ui/react';

// Payload for API requests
export interface PayloadInterface {
   clientId: string;
   category: string;
   startDate: string;
   endDate: string;
   prevStartDate: string;
   prevEndDate: string;
}

// Individual KPI breakdown
export interface KpiBreakdownItem {
   value: number;
   kpi_display_name: string;
   unit: string;
   category: string;
   up: boolean;
   sub_items?: BreakDown | null; // recursive breakdown
   kpi_type: string;
}

// Recursive breakdown type
export interface BreakDown {
   [key: string]: KpiBreakdownItem;
}

// Individual KPI data
export interface KpiData {
   category: string;
   kpi_names: string;
   kpi_unit: string;
   kpi_display_name: string;
   kpi_type: string;
   daily_breakdown: BreakDown | null;
   current_total_value: number;
   previous_total_value: number;
   current_allData: KpiTimelineItem[];
   previous_allData: KpiTimelineItem[];
}

export interface KpiDataWithMeta extends KpiData {
   visible: boolean;
   pinned: boolean;
   order: number;
}

// Timeline data points for a KPI
export interface KpiTimelineItem {
   kpi_value: number;
   date: string;
}

// Dashboard response
export interface DataDashboardResponse {
   success: boolean;
   message: string;
   data: Connector;
   inactive?: boolean; // indicates if the connector is inactive
   anomaly?: Anomalies;
}

export interface Anomalies {
   [kpiName: string]: boolean | null; // KPI anomaly can be true/false/null
}

export interface Connector {
   [key: string]: ConnectorKpiData;
}

export interface ConnectorKpiData {
   [kpiName: string]: KpiData;
}

export interface KpiCardProps {
   data: KpiDataWithMeta;
}

export interface ConnectorKpiDataWithMeta {
   [kpiName: string]: KpiDataWithMeta;
}

export interface DashboardDataCardProps {
   keyValue: string;
   label: string;
   connectorKey: string;
   data: Connector;
   metaData: KPIMeta[] | undefined;
   anomalies?: Anomalies;
   allConnectorsLoaded?: boolean;
}

export interface KPIMeta {
   category: string;
   kpi: string;
   visible: boolean;
   pinned: boolean;
   kpi_display_name: string;
   pinned_order: number;
   visible_order: number;
}

export interface DashboardCurrency {
   success: boolean;
   message: string;
   data: {
      meta: string;
      google: string;
   };
}
// Optional: utility type for filtered KPIs
export type FilteredKpi = KpiData & { order?: number; pinned?: boolean };

export interface ChartProp {
   kpiDetails: KpiDataWithMeta;
   value?: KpiTimelineItem[];
   anomaly?: boolean | null;
}

export interface DualLineChartProps {
   kpiDetails: KpiDataWithMeta;
   value?: KpiTimelineItem[];
}

export interface alertsAndOpportunities {
   kpiName: string;
   direction: 'up' | 'down';
   isPositive: boolean;
   percentage: string;
   message: string;
   category: string;
   remove: boolean;
}
export interface hiddenAnomalyAlerts extends alertsAndOpportunities {
   remove: boolean;
}

export interface KPIStoreRange {
   end: string;
   start: string;
}

export interface TimeRange {
   endDate: Date;
   startDate: Date;
}

export interface DateRange {
   startDate: Date;
   endDate: Date;
   key: string;
}

export interface RangeState {
   show: boolean;
   dateRange: DateRange[];
}

export interface KPIRange {
   end: Date;
   start: Date;
}

export interface DateRange {
   startDate: Date;
   endDate: Date;
   key: string;
}

export interface KPISummaryPayload {
   data: GPTData[];
   kpi: string;
   category: string;
   currency: string;
}

export interface GPTData {
   xaxis: string;
   yaxis: number;
}

export interface PinPayload {
   clientId: string;
   category: string;
   kpis: string[];
   pinned: boolean;
}
export interface VisblePayload {
   clientId: string;
   category: string;
   kpis: string[];
   visible: boolean;
}

export interface PinVisibleOrderPayload {
   clientId: string;
   kpiOrder: {
      kpi: string;
      order: number;
   }[];
   pinOrder: boolean;
}

export interface KPIAnomalyCausePayload {
   client_id: string;
   start_date: string;
   end_date: string;
   kpi: string;
}

export interface AnomalyCause {
   [key: string]: string;
}
export interface Ranges {
   label: string;
   range: () => TimeRange;
}

export interface Edorment {
   [key: string]: string;
}

export type KpiCategoryKeys = keyof typeof KPICategory;
export interface MetaCatAgg {
   [key: string]: KPIMeta[];
}

export interface SummaryProp {
   summary: string[];
   isFetching: boolean;
   error: string | null;
}

export interface MetricReport {
   clientId: string;
   user_id?: string;
   title: string;
   time: string;
   frequency: string;
   interval: string;
   start_date: string;
   end_date: string;
   date: string;
   emails: string;
   kpis: MetricKPI[] | string;
   username: string;
   reportId: string;
   report_id?: string;
   client_id?: string;
   created_at?: string;
   is_auto_report?: boolean;
   is_subscribed?: boolean;
}
export interface MetricKPI {
   category: string;
   kpi: string;
   kpi_display_name: string;
}

export interface OptionsMore {
   seriesIndex: number;
   dataPointIndex: number;
   w: {
      globals: {
         series: string[];
      };
   };
}

export interface UpdateReportPayload {
   clientId: string;
   user_id: string;
   reportId: string;
   title: string;
   time: string;
   frequency: string;
   interval: string;
   start_date: string;
   end_date: string;
   date: string;
   emails: string;
   username: string;
   kpis: MetricKPI[] | string;
}

export interface GetReportPayload {
   clientId: string;
   userId: string;
}
export interface DeleteReportPayload {
   clientId: string;
   reportId: string;
}
export interface PauseReportPayload {
   clientId: string;
   isSubscribed: boolean;
   reportId: string;
}
export interface UpdateReportPayload {
   clientId: string;
   user_id: string;
   reportId: string;
   title: string;
   time: string;
   frequency: string;
   interval: string;
   start_date: string;
   end_date: string;
   date: string;
   emails: string;
   username: string;
   kpis: MetricKPI[] | string;
}

export interface KPIDetails {
   allData: KpiData[];
   totalVal: number;
   category: string;
   unit: string;
   displayName: string;
   kpi_names: string;
   pinned?: boolean;
   visible?: boolean;
   order?: number;
   daily_breakdown?: BreakDown | null;
   percentage_change?: number | null;
   standard_deviation?: number | null;
}
export interface TooltipContentProps {
   children: React.ReactNode;
   category?: string;
   kpi?: string;
   label?: string;
   hasArrow?: boolean;
   placement?: PlacementWithLogical;
}
