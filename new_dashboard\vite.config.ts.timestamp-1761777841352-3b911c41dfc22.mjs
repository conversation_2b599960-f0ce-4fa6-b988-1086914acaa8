// vite.config.ts
import { defineConfig } from 'file:///C:/Manivanna/Work/repo/new_dashboard/node_modules/vite/dist/node/index.js';
import path from 'path';
import react from 'file:///C:/Manivanna/Work/repo/new_dashboard/node_modules/@vitejs/plugin-react/dist/index.js';
import tailwindcss from 'file:///C:/Manivanna/Work/repo/new_dashboard/node_modules/@tailwindcss/vite/dist/index.mjs';
import svgr from 'file:///C:/Manivanna/Work/repo/new_dashboard/node_modules/vite-plugin-svgr/dist/index.js';
var __vite_injected_original_dirname =
   'C:\\Manivanna\\Work\\repo\\new_dashboard';
var vite_config_default = defineConfig({
   build: {
      rollupOptions: {
         input: 'index.html',
      },
   },
   envDir: 'config/env',
   plugins: [react(), tailwindcss(), svgr()],
   // Add React plugin + tailwind plugin
   resolve: {
      alias: {
         '@': path.resolve(__vite_injected_original_dirname, './src'),
      },
   },
   server: {
      port: 3e3,
      // Set the port to 3000
      hmr: true,
   },
});
export { vite_config_default as default };
//# sourceMappingURL=data:application/json;base64,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
