import { Edorment } from '../../pages/dashboard/utils/interface';

export const KPICategory: {
   [key: string]: string;
} = {
   facebookads: 'Meta Ads',
   store: 'Shopify Store',
   web: 'Web Analytics',
   pinned: 'Pinned',
   googleads: 'Google Ads',
   amazon_selling_partner: 'Amazon Selling Partner',
   amazon_ads: 'Amazon Ads',
   overall_metrics: 'Overall Metrics',
};
export const CategoryChannel: {
   [key: string]: string;
} = {
   facebookads: 'facebookads',
   store: 'shopify',
   web: 'googleanalytics',
   pinned: 'Pinned',
   googleads: 'googleads',
   amazon_selling_partner: 'amazon_selling_partner',
   amazon_ads: 'amazon_ads',
   overall_metrics: 'Overall Metrics',
};
export const startEdornment: Edorment = {
   inr: decodeHtml('&#8377;'),
   eur: decodeHtml('&#8364;'),
};
export const endEdornment: Edorment = {
   percentage: decodeHtml('&percnt;'),
};
function decodeHtml(html: string): string {
   const txt = document.createElement('textarea');
   txt.innerHTML = html;
   return txt.value;
}

export const noZeroKPI = [
   'cpp',
   'roas',
   'cpc',
   'cpm',
   'google_roas',
   'google_cpc',
   'google_cpm',
   'cpv',
   'cost_per_lead',
];
export const GROUP_BY_DAYS: {
   [key: string]: number;
} = {
   day: 1,
   week: 6,
   month: 29,
   quarter: 89,
};
