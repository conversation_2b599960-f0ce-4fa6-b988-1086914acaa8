import {
   Box,
   Flex,
   Skeleton,
   SkeletonText,
   useColorMode,
} from '@chakra-ui/react';

const PerformanceAndAlertsSkeleton = () => {
   const { colorMode } = useColorMode();

   const bgColor = colorMode === 'dark' ? 'gray.800' : 'white';
   const shadowColor =
      colorMode === 'dark'
         ? '1px 1px 10px 1px #00000033'
         : '1px 1px 10px 1px #cccccc33';

   return (
      <Flex w='100%' gap={4} mb={4} flexWrap='wrap'>
         <Box
            flex='1.8'
            minW='65%'
            bg={bgColor}
            boxShadow={shadowColor}
            borderRadius='md'
            p={5}
         >
            <Skeleton height='20px' width='160px' mb={4} />
            <Skeleton height='200px' borderRadius='md' />
         </Box>

         <Box
            flex='1'
            minW='30%'
            bg={bgColor}
            boxShadow={shadowColor}
            borderRadius='md'
            p={5}
         >
            <Skeleton height='20px' width='180px' mb={4} />
            <SkeletonText mt='4' noOfLines={3} spacing='4' />
         </Box>
      </Flex>
   );
};

export default PerformanceAndAlertsSkeleton;
