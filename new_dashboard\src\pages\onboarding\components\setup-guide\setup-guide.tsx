import { Box, Icon, Text } from '@chakra-ui/react';
import { useAppSelector } from '../../../../store/store';

import './setup-guide.scss';
import {
   CurrentStepIndicator,
   IncompleteStepIndicator,
   CompleteStepIndicator,
} from '../../utils/indicators';

const SetupGuide = () => {
   const { registerProgress } = useAppSelector((state) => state.onboarding);

   const renderStepIcon = (
      currentStep: number,
      registerProgress: string,
   ): React.ElementType => {
      const progress: number = Number(registerProgress.split(' ')[1]);
      if (progress === currentStep) return CurrentStepIndicator;
      if (progress > currentStep) return CompleteStepIndicator;
      return IncompleteStepIndicator;
   };

   return (
      <Box
         width='89%'
         height='50%'
         border='1px solid #fff'
         borderRadius='8px'
         zIndex={2}
         display='flex'
         flexDirection='column'
         justifyContent='space-between'
         alignItems='center'
         padding='10px'
         color='#fff'
         className='SetupGuide'
      >
         <Box className='remaining-time' borderBottom={'1px solid #fff'}>
            Usual time to complete: <span className='time'>6 min</span>
         </Box>
         <Box className='steps' borderBottom={'1px solid #fff'}>
            <Icon as={renderStepIcon(1, registerProgress)} />
            <Text className='step-names'>Welcome</Text>
         </Box>
         <Box className='steps' borderBottom={'1px solid #fff'}>
            <Icon as={renderStepIcon(2, registerProgress)} />
            <Text className='step-names'>Account Setup</Text>
         </Box>
         <Box className='steps' borderBottom={'1px solid #fff'}>
            <Icon as={renderStepIcon(3, registerProgress)} />
            <Text className='step-names'>Integrations</Text>
         </Box>
         <Box className='steps' borderBottom={'1px solid #fff'}>
            <Icon as={renderStepIcon(4, registerProgress)} />
            <Text className='step-names'>Flable Pixel</Text>
         </Box>
         <Box className='steps' borderBottom={0}>
            <Icon as={renderStepIcon(5, registerProgress)} />
            <Text className='step-names'>Competitors</Text>
         </Box>
      </Box>
   );
};

export default SetupGuide;
