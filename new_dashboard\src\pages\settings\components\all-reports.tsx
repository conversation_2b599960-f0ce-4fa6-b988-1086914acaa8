import {
   <PERSON><PERSON>,
   <PERSON><PERSON>,
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON><PERSON><PERSON>,
   <PERSON><PERSON><PERSON><PERSON>,
   <PERSON><PERSON><PERSON><PERSON>,
   <PERSON>kel<PERSON>,
   Spinner,
   Text,
   useToast,
} from '@chakra-ui/react';
import { Dispatch, SetStateAction, useEffect } from 'react';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import settingsService from '../../../api/service/settings/index';
import { SettingsQueryKeys } from '../../dashboard/utils/query-keys';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { MetricKPI, MetricReport } from '../../dashboard/utils/interface';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { GROUP_BY_RANGE } from '../../dashboard/utils/default-variables';
import { getUserTime } from '../../dashboard/utils/helpers';
import { Md<PERSON><PERSON><PERSON>, MdEdit } from 'react-icons/md';
import { MdOutlinePauseCircleFilled } from 'react-icons/md';
import { IoIosPlay } from 'react-icons/io';

interface AllReportsProps {
   seteditReport: Dispatch<SetStateAction<MetricReport>>;
   setallReports: Dispatch<SetStateAction<boolean>>;
}

function AllReports(props: AllReportsProps) {
   const toast = useToast();
   useEffect(() => {
      getAllReports().catch(console.log);
   }, []);
   const userDetails: {
      [key: string]: string;
   } = LocalStorageService.getItem(Keys.FlableUserDetails) || {};
   const handleDeleteReportSuccess = () => {
      toast({
         title: 'Success',
         description: 'Report deleted successfully',
         status: 'success',
         duration: 5000,
         isClosable: true,
      });
      getAllReports().catch(console.log);
   };
   const handlePauseorResumeReportSuccess = () => {
      toast({
         title: 'Success',
         description: 'Report Edited successfully',
         status: 'success',
         duration: 5000,
         isClosable: true,
      });
      getAllReports().catch(console.log);
   };
   const {
      mutate: deleteEmailReport,
      errorMessage: deleteMailErr,
      isPending: deleteMailLoading,
   } = useApiMutation({
      queryKey: [SettingsQueryKeys.deleteEmailReport],
      mutationFn: settingsService.deleteReport,
      onSuccessHandler: handleDeleteReportSuccess,
      onError: handleError,
   });
   const {
      mutate: pauseResumeAutoReport,
      errorMessage: pauseResumeReportErr,
      isPending: pauseResumeErrorLoading,
   } = useApiMutation({
      queryKey: [SettingsQueryKeys.pauseAutoReport],
      mutationFn: settingsService.pauseResumeAutoReport,
      onSuccessHandler: handlePauseorResumeReportSuccess,
      onError: handleErrorPauseorResume,
   });

   function handleErrorPauseorResume() {
      if (pauseResumeReportErr) {
         toast({
            title: 'Error',
            description: pauseResumeReportErr,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      }
   }

   function handleError() {
      if (deleteMailErr) {
         toast({
            title: 'Error',
            description: deleteMailErr,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      }
   }
   const {
      data: reportsData,
      isFetching: reportsLoad,
      errorMessage: reportsErr,
      refetch: getAllReports,
   } = useApiQuery({
      queryKey: [SettingsQueryKeys.getEmailReports],
      queryFn: () =>
         settingsService.getReports({
            clientId: userDetails.client_id,
            userId: userDetails.user_id,
         }),
      getInitialData: () => [],
      enabled: false,
   });
   const handleReportDelete = (report: MetricReport) => {
      deleteEmailReport({
         clientId: report.client_id || '',
         reportId: report.report_id || '',
      });
   };
   const handleReportPauseResume = (report: MetricReport) => {
      pauseResumeAutoReport({
         clientId: report.client_id || '',
         isSubscribed: !report.is_subscribed,
         reportId: report.report_id || '',
      });
   };

   const handleReportEdit = (report: MetricReport) => {
      props.seteditReport(report);
      props.setallReports(false);
   };
   if (reportsErr) {
      toast({
         title: 'Error',
         description: reportsErr,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });
   }

   return (
      <Flex flexWrap={'wrap'} gap={5} p={5}>
         {reportsLoad ? (
            <>
               {' '}
               <Skeleton
                  height='250px'
                  borderRadius={'10px'}
                  minWidth={'40%'}
                  flex={1}
               />
               <Skeleton
                  height='250px'
                  borderRadius={'10px'}
                  minWidth={'40%'}
                  flex={1}
               />
               <Skeleton
                  height='250px'
                  borderRadius={'10px'}
                  minWidth={'40%'}
                  flex={1}
               />
               <Skeleton
                  height='250px'
                  borderRadius={'10px'}
                  minWidth={'40%'}
                  flex={1}
               />
            </>
         ) : reportsData?.length ? (
            reportsData
               .sort(
                  (a, b) =>
                     new Date(a.created_at || '').getTime() -
                     new Date(b.created_at || '').getTime(),
               )
               .map((report) => {
                  const kpis = JSON.parse(report.kpis as string) as MetricKPI[];

                  return (
                     <Flex
                        id={report.reportId}
                        className='report-card'
                        p={4}
                        border={'1px solid #DFDFDF'}
                        borderRadius={'10px'}
                        direction={'column'}
                        flex={1}
                        minWidth={'40%'}
                        key={report.reportId}
                     >
                        <Flex
                           borderBottom={'1px solid #DFDFDF'}
                           pb={3}
                           justifyContent={'space-between'}
                        >
                           <Heading fontWeight={'500'} fontSize={'16px'}>
                              {report.title}
                           </Heading>
                           <Menu>
                              <MenuButton>
                                 <BsThreeDotsVertical />
                              </MenuButton>
                              <MenuList p={0}>
                                 <MenuItem
                                    onClick={() => handleReportEdit(report)}
                                 >
                                    {' '}
                                    <MdEdit
                                       style={{ paddingRight: '5px' }}
                                       size={24}
                                    />
                                    Edit Report
                                 </MenuItem>
                                 {!report.is_auto_report && (
                                    <MenuItem
                                       disabled={deleteMailLoading}
                                       onClick={() =>
                                          handleReportDelete(report)
                                       }
                                    >
                                       <MdDelete
                                          style={{ paddingRight: '5px' }}
                                          size={24}
                                       />
                                       Delete{' '}
                                       {deleteMailLoading && (
                                          <Spinner
                                             ml={2}
                                             thickness='4px'
                                             speed='0.65s'
                                             emptyColor='gray.200'
                                             color='blue.500'
                                             size='sm'
                                          />
                                       )}
                                    </MenuItem>
                                 )}
                                 {report.is_auto_report &&
                                    report.is_subscribed && (
                                       <MenuItem
                                          disabled={pauseResumeErrorLoading}
                                          onClick={() =>
                                             handleReportPauseResume(report)
                                          }
                                       >
                                          <MdOutlinePauseCircleFilled
                                             style={{ paddingRight: '5px' }}
                                             size={24}
                                          />
                                          Pause{' '}
                                          {pauseResumeErrorLoading && (
                                             <Spinner
                                                ml={2}
                                                thickness='4px'
                                                speed='0.65s'
                                                emptyColor='gray.200'
                                                color='blue.500'
                                                size='sm'
                                             />
                                          )}
                                       </MenuItem>
                                    )}
                                 {report.is_auto_report &&
                                    !report.is_subscribed && (
                                       <MenuItem
                                          disabled={pauseResumeErrorLoading}
                                          onClick={() =>
                                             handleReportPauseResume(report)
                                          }
                                       >
                                          <IoIosPlay
                                             style={{ paddingRight: '5px' }}
                                             size={24}
                                          />
                                          Resume{' '}
                                          {pauseResumeErrorLoading && (
                                             <Spinner
                                                ml={2}
                                                thickness='4px'
                                                speed='0.65s'
                                                emptyColor='gray.200'
                                                color='blue.500'
                                                size='sm'
                                             />
                                          )}
                                       </MenuItem>
                                    )}
                              </MenuList>
                           </Menu>
                        </Flex>
                        <Flex gap={2} pt={3}>
                           <Flex direction={'column'} gap={5} flex={1}>
                              <Flex direction={'column'}>
                                 <Text className='report-sub-head'>
                                    Interval
                                 </Text>
                                 <Text>{report.interval}</Text>
                              </Flex>
                              <Flex direction={'column'}>
                                 <Text className='report-sub-head'>
                                    Frequency
                                 </Text>
                                 <Text>
                                    {GROUP_BY_RANGE[report.frequency]},{' '}
                                    {getUserTime(report.time)}
                                 </Text>
                              </Flex>
                              <Flex direction={'column'}>
                                 <Text className='report-sub-head'>
                                    Recipents
                                 </Text>
                                 <Text>{report.emails}</Text>
                              </Flex>
                           </Flex>
                           <Flex direction={'column'} flex={1}>
                              <Text className='report-sub-head'>Summary</Text>
                              <Text>
                                 {kpis
                                    .slice(0, 10)
                                    .map((x) => x.kpi_display_name)
                                    .join(', ')}
                              </Text>
                              {kpis.length > 10 && (
                                 <Text color={'#7F56D9'} mt={1}>
                                    +{kpis.length - 10} More Kpis
                                 </Text>
                              )}
                           </Flex>
                        </Flex>
                     </Flex>
                  );
               })
         ) : (
            <Text flex={1} textAlign={'center'}>
               No Active Reports available.
            </Text>
         )}
      </Flex>
   );
}

export default AllReports;
