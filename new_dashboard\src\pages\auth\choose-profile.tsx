import { useApiQuery } from '../../hooks/react-query-hooks';
import userManagementEndpoints, {
   AllUserDetails,
} from '../../api/service/users';
import { LocalStorageService, Keys } from '../../utils/local-storage';
import {
   Avatar,
   Box,
   Flex,
   Icon,
   Image,
   Spinner,
   Stack,
   Text,
} from '@chakra-ui/react';
import { appStrings } from '../../utils/strings/app-strings';
import ICON from '../../assets/image/flableaiBrandlogo.png';
import { IoArrowForward } from 'react-icons/io5';
import { useNavigate } from 'react-router-dom';
import { AuthUser } from '../../types/auth';

const ChooseProfilePage = () => {
   const navigate = useNavigate();

   const { data: allProfiles, isFetching } = useApiQuery({
      queryKey: ['allProfiles'],
      queryFn: () =>
         userManagementEndpoints.getAllProfiles({
            email_address: LocalStorageService.getItem(Keys.UserName) as string,
         }),
      selectHandler: (data) => {
         if (data.details.length === 1) {
            const userDetails: AuthUser = {
               email: data.details[0].email_address,
               client_id: data.details[0].client_id,
               company_name: data.details[0].company_name,
               company_url: data.details[0].company_url,
               organization_type: data.details[0].organization_type,
               user_role: data.details[0].user_role,
               fullName: data.details[0].full_name,
               user_id: data.details[0].user_id,
            };

            LocalStorageService.setItem(Keys.FlableUserDetails, userDetails);
            LocalStorageService.setItem(
               Keys.UserName,
               data.details[0].email_address,
            );
            LocalStorageService.setItem(
               Keys.ClientId,
               data.details[0].client_id,
            );
         }
         return data;
      },
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
   });

   const handleProfileSelection = (profile: AllUserDetails) => {
      const userDetails = {
         email: profile.email_address,
         client_id: profile.client_id,
         company_name: profile.company_name,
         company_url: profile.company_url,
         organization_type: profile.organization_type,
         fullName: profile.full_name,
         user_role: profile.user_role,
         user_id: profile.user_id,
      };

      LocalStorageService.setItem(Keys.FlableUserDetails, userDetails);
      LocalStorageService.setItem(Keys.UserName, profile.email_address);
      LocalStorageService.setItem(Keys.ClientId, profile.client_id);

      navigate('/');
   };

   const returnProfileItem = (profile: AllUserDetails) => {
      return (
         <Flex
            alignItems='center'
            justifyContent='space-between'
            boxShadow='0px 0px 2px 0px #00000040'
            sx={{
               cursor: 'pointer',
               '&:hover': {
                  boxShadow: '0px 0px 4px 0px #00000080',
               },
            }}
            padding={3}
            borderRadius={5}
            onClick={() => handleProfileSelection(profile)}
         >
            <Flex alignItems='center' justifyContent='space-between' gap={3}>
               <Avatar size='sm' name={profile.company_url} src='' />
               <Text>{profile.company_url}</Text>
            </Flex>
            <Icon as={IoArrowForward} sx={{ cursor: 'pointer' }} />
         </Flex>
      );
   };

   if (isFetching) {
      return (
         <Flex
            width='100%'
            height='100vh'
            justifyContent='center'
            alignItems='center'
         >
            <Spinner size='xl' />
         </Flex>
      );
   }

   return (
      <>
         <Box
            display='flex'
            justifyContent='center'
            alignItems='center'
            mt={10}
            mb={10}
         >
            <Image src={ICON} alt='Flable Icon' w='10%' />
            <Box ml={2} fontSize='xl' fontWeight='bold'>
               {appStrings.companyName}
            </Box>
         </Box>
         <Box textAlign='center'>
            <Text fontWeight='bold'>Choose Profile</Text>
            <Text mt={2} as='p'>
               Choose the profile into which you want to log in
            </Text>
         </Box>
         <Box mt={8} height='50%' padding='5px' overflowY='scroll'>
            <Stack spacing={3}>
               {allProfiles?.details
                  .filter((profile) => profile.is_active)
                  .map((profile: AllUserDetails) => returnProfileItem(profile))}
            </Stack>
         </Box>
      </>
   );
};

export default ChooseProfilePage;
