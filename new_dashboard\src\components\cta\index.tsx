import { Button } from '../ui/button';

type CtaProps = {
   title?: string;
   desc?: string;
   actionLabel?: string;
};

const Cta = ({ title, desc, actionLabel }: CtaProps) => {
   return (
      <section className='flex py-10 items-center justify-around '>
         <div className='flex flex-col space-y-6 items-center justify-around  max-w-2/5 text-center '>
            {title && <h6 className='head6 text-jet font-semibold'>{title}</h6>}
            {desc && <p className='text-charcoal para4 font-medium'>{desc}</p>}
            {actionLabel && <Button>{actionLabel}</Button>}
         </div>
      </section>
   );
};

export default Cta;
