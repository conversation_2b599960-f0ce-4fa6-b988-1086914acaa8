import { AnalyticsAgentChat } from '@/api/service/agentic-workflow/analytics-agent';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type Agents = 'analytics-agent';

interface InitialState {
   currentAgent: Agents;
   currentHistory: {
      'analytics-agent': AnalyticsAgentChat[];
   };
   aggregatedColumns: {
      enabled: string[];
      disabled: string[];
   };
   visibleColumns: {
      enabled: string[];
      disabled: string[];
   };
}

const initialState: InitialState = {
   currentAgent: 'analytics-agent',
   currentHistory: {
      'analytics-agent': [],
   },
   aggregatedColumns: {
      enabled: [],
      disabled: [],
   },
   visibleColumns: {
      enabled: [],
      disabled: [],
   },
};

const marcoSlice = createSlice({
   name: 'marco',
   initialState,
   reducers: {
      setCurrentAgent: (state, action: PayloadAction<Agents>) => {
         state.currentAgent = action.payload;
      },
      setCurrentHistory: (
         state,
         action: PayloadAction<{
            agent: Agents;
            history: AnalyticsAgentChat[];
         }>,
      ) => {
         const { agent, history } = action.payload;

         const merged = [
            ...state.currentHistory[agent as 'analytics-agent'],
            ...history,
         ].filter((chat) => chat.session_id);

         const uniqueBySessionId = Array.from(
            new Map(merged.map((chat) => [chat.session_id, chat])).values(),
         );

         if (agent === 'analytics-agent') {
            state.currentHistory['analytics-agent'] = uniqueBySessionId;
         }
      },
      setAggregatedColumns: (
         state,
         action: PayloadAction<{ enabled: string[]; disabled: string[] }>,
      ) => {
         state.aggregatedColumns = action.payload;
      },
      setVisibleColumns: (
         state,
         action: PayloadAction<{ enabled: string[]; disabled: string[] }>,
      ) => {
         state.visibleColumns = action.payload;
      },
   },
});

export const {
   setCurrentAgent,
   setCurrentHistory,
   setAggregatedColumns,
   setVisibleColumns,
} = marcoSlice.actions;

export default marcoSlice.reducer;
